/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/*
 * This file should be your main import to use Prisma. Through it you get access to all the models, enums, and input types.
 *
 * 🟢 You can import this file directly.
 */

import * as path from "node:path";
import * as process from "node:process";
import { fileURLToPath } from "node:url";

globalThis["__dirname"] = path.dirname(fileURLToPath(import.meta.url));

import type * as runtime from "@prisma/client/runtime/library";
import * as $Enums from "./enums";
import * as $Class from "./internal/class";
import * as Prisma from "./internal/prismaNamespace";

export * as $Enums from "./enums";
/**
 * ## Prisma Client
 *
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export const PrismaClient = $Class.getPrismaClientClass(__dirname);
export type PrismaClient<
	LogOpts extends Prisma.LogLevel = never,
	OmitOpts extends
		Prisma.PrismaClientOptions["omit"] = Prisma.PrismaClientOptions["omit"],
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = $Class.PrismaClient<LogOpts, OmitOpts, ExtArgs>;
export { Prisma };

// file annotations for bundling tools to include these files
path.join(__dirname, "libquery_engine-darwin-arm64.dylib.node");
path.join(
	process.cwd(),
	"prisma/generated/libquery_engine-darwin-arm64.dylib.node",
);

/**
 * Model User
 *
 */
export type User = Prisma.UserModel;
/**
 * Model Session
 *
 */
export type Session = Prisma.SessionModel;
/**
 * Model Account
 *
 */
export type Account = Prisma.AccountModel;
/**
 * Model Verification
 *
 */
export type Verification = Prisma.VerificationModel;
/**
 * Model Organization
 *
 */
export type Organization = Prisma.OrganizationModel;
/**
 * Model OrganizationMember
 *
 */
export type OrganizationMember = Prisma.OrganizationMemberModel;
/**
 * Model Invitation
 *
 */
export type Invitation = Prisma.InvitationModel;

export type OrganizationType = $Enums.OrganizationType;
export const OrganizationType = $Enums.OrganizationType;

export type Role = $Enums.Role;
export const Role = $Enums.Role;

export type InvitationStatus = $Enums.InvitationStatus;
export const InvitationStatus = $Enums.InvitationStatus;
