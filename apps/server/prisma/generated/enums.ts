/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/*
 * This file exports all enum related types from the schema.
 *
 * 🟢 You can import this file directly.
 */

export const OrganizationType = {
	PERSONAL: "PERSONAL",
	TEAM: "TEAM",
	ENTERPRISE: "ENTERPRISE",
} as const;

export type OrganizationType =
	(typeof OrganizationType)[keyof typeof OrganizationType];

export const Role = {
	ADMIN: "ADMIN",
	COLLABORATOR: "COLLABORATOR",
	USER: "USER",
} as const;

export type Role = (typeof Role)[keyof typeof Role];

export const InvitationStatus = {
	PENDING: "PENDING",
	ACCEPTED: "ACCEPTED",
	EXPIRED: "EXPIRED",
	REVOKED: "REVOKED",
} as const;

export type InvitationStatus =
	(typeof InvitationStatus)[keyof typeof InvitationStatus];
