/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/*
 * This file exports the `OrganizationMember` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import type * as runtime from "@prisma/client/runtime/library";
import type * as $Enums from "../enums";
import type * as Prisma from "../internal/prismaNamespace";

/**
 * Model OrganizationMember
 *
 */
export type OrganizationMemberModel =
	runtime.Types.Result.DefaultSelection<Prisma.$OrganizationMemberPayload>;

export type AggregateOrganizationMember = {
	_count: OrganizationMemberCountAggregateOutputType | null;
	_min: OrganizationMemberMinAggregateOutputType | null;
	_max: OrganizationMemberMaxAggregateOutputType | null;
};

export type OrganizationMemberMinAggregateOutputType = {
	id: string | null;
	userId: string | null;
	organizationId: string | null;
	role: $Enums.Role | null;
	joinedAt: Date | null;
	updatedAt: Date | null;
};

export type OrganizationMemberMaxAggregateOutputType = {
	id: string | null;
	userId: string | null;
	organizationId: string | null;
	role: $Enums.Role | null;
	joinedAt: Date | null;
	updatedAt: Date | null;
};

export type OrganizationMemberCountAggregateOutputType = {
	id: number;
	userId: number;
	organizationId: number;
	role: number;
	joinedAt: number;
	updatedAt: number;
	_all: number;
};

export type OrganizationMemberMinAggregateInputType = {
	id?: true;
	userId?: true;
	organizationId?: true;
	role?: true;
	joinedAt?: true;
	updatedAt?: true;
};

export type OrganizationMemberMaxAggregateInputType = {
	id?: true;
	userId?: true;
	organizationId?: true;
	role?: true;
	joinedAt?: true;
	updatedAt?: true;
};

export type OrganizationMemberCountAggregateInputType = {
	id?: true;
	userId?: true;
	organizationId?: true;
	role?: true;
	joinedAt?: true;
	updatedAt?: true;
	_all?: true;
};

export type OrganizationMemberAggregateArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Filter which OrganizationMember to aggregate.
	 */
	where?: Prisma.OrganizationMemberWhereInput;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
	 *
	 * Determine the order of OrganizationMembers to fetch.
	 */
	orderBy?:
		| Prisma.OrganizationMemberOrderByWithRelationInput
		| Prisma.OrganizationMemberOrderByWithRelationInput[];
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
	 *
	 * Sets the start position
	 */
	cursor?: Prisma.OrganizationMemberWhereUniqueInput;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
	 *
	 * Take `±n` OrganizationMembers from the position of the cursor.
	 */
	take?: number;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
	 *
	 * Skip the first `n` OrganizationMembers.
	 */
	skip?: number;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
	 *
	 * Count returned OrganizationMembers
	 **/
	_count?: true | OrganizationMemberCountAggregateInputType;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
	 *
	 * Select which fields to find the minimum value
	 **/
	_min?: OrganizationMemberMinAggregateInputType;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
	 *
	 * Select which fields to find the maximum value
	 **/
	_max?: OrganizationMemberMaxAggregateInputType;
};

export type GetOrganizationMemberAggregateType<
	T extends OrganizationMemberAggregateArgs,
> = {
	[P in keyof T & keyof AggregateOrganizationMember]: P extends
		| "_count"
		| "count"
		? T[P] extends true
			? number
			: Prisma.GetScalarType<T[P], AggregateOrganizationMember[P]>
		: Prisma.GetScalarType<T[P], AggregateOrganizationMember[P]>;
};

export type OrganizationMemberGroupByArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	where?: Prisma.OrganizationMemberWhereInput;
	orderBy?:
		| Prisma.OrganizationMemberOrderByWithAggregationInput
		| Prisma.OrganizationMemberOrderByWithAggregationInput[];
	by:
		| Prisma.OrganizationMemberScalarFieldEnum[]
		| Prisma.OrganizationMemberScalarFieldEnum;
	having?: Prisma.OrganizationMemberScalarWhereWithAggregatesInput;
	take?: number;
	skip?: number;
	_count?: OrganizationMemberCountAggregateInputType | true;
	_min?: OrganizationMemberMinAggregateInputType;
	_max?: OrganizationMemberMaxAggregateInputType;
};

export type OrganizationMemberGroupByOutputType = {
	id: string;
	userId: string;
	organizationId: string;
	role: $Enums.Role;
	joinedAt: Date;
	updatedAt: Date;
	_count: OrganizationMemberCountAggregateOutputType | null;
	_min: OrganizationMemberMinAggregateOutputType | null;
	_max: OrganizationMemberMaxAggregateOutputType | null;
};

type GetOrganizationMemberGroupByPayload<
	T extends OrganizationMemberGroupByArgs,
> = Prisma.PrismaPromise<
	Array<
		Prisma.PickEnumerable<OrganizationMemberGroupByOutputType, T["by"]> & {
			[P in keyof T &
				keyof OrganizationMemberGroupByOutputType]: P extends "_count"
				? T[P] extends boolean
					? number
					: Prisma.GetScalarType<T[P], OrganizationMemberGroupByOutputType[P]>
				: Prisma.GetScalarType<T[P], OrganizationMemberGroupByOutputType[P]>;
		}
	>
>;

export type OrganizationMemberWhereInput = {
	AND?:
		| Prisma.OrganizationMemberWhereInput
		| Prisma.OrganizationMemberWhereInput[];
	OR?: Prisma.OrganizationMemberWhereInput[];
	NOT?:
		| Prisma.OrganizationMemberWhereInput
		| Prisma.OrganizationMemberWhereInput[];
	id?: Prisma.StringFilter<"OrganizationMember"> | string;
	userId?: Prisma.StringFilter<"OrganizationMember"> | string;
	organizationId?: Prisma.StringFilter<"OrganizationMember"> | string;
	role?: Prisma.EnumRoleFilter<"OrganizationMember"> | $Enums.Role;
	joinedAt?: Prisma.DateTimeFilter<"OrganizationMember"> | Date | string;
	updatedAt?: Prisma.DateTimeFilter<"OrganizationMember"> | Date | string;
	user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>;
	organization?: Prisma.XOR<
		Prisma.OrganizationScalarRelationFilter,
		Prisma.OrganizationWhereInput
	>;
};

export type OrganizationMemberOrderByWithRelationInput = {
	id?: Prisma.SortOrder;
	userId?: Prisma.SortOrder;
	organizationId?: Prisma.SortOrder;
	role?: Prisma.SortOrder;
	joinedAt?: Prisma.SortOrder;
	updatedAt?: Prisma.SortOrder;
	user?: Prisma.UserOrderByWithRelationInput;
	organization?: Prisma.OrganizationOrderByWithRelationInput;
};

export type OrganizationMemberWhereUniqueInput = Prisma.AtLeast<
	{
		id?: string;
		userId_organizationId?: Prisma.OrganizationMemberUserIdOrganizationIdCompoundUniqueInput;
		AND?:
			| Prisma.OrganizationMemberWhereInput
			| Prisma.OrganizationMemberWhereInput[];
		OR?: Prisma.OrganizationMemberWhereInput[];
		NOT?:
			| Prisma.OrganizationMemberWhereInput
			| Prisma.OrganizationMemberWhereInput[];
		userId?: Prisma.StringFilter<"OrganizationMember"> | string;
		organizationId?: Prisma.StringFilter<"OrganizationMember"> | string;
		role?: Prisma.EnumRoleFilter<"OrganizationMember"> | $Enums.Role;
		joinedAt?: Prisma.DateTimeFilter<"OrganizationMember"> | Date | string;
		updatedAt?: Prisma.DateTimeFilter<"OrganizationMember"> | Date | string;
		user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>;
		organization?: Prisma.XOR<
			Prisma.OrganizationScalarRelationFilter,
			Prisma.OrganizationWhereInput
		>;
	},
	"id" | "userId_organizationId"
>;

export type OrganizationMemberOrderByWithAggregationInput = {
	id?: Prisma.SortOrder;
	userId?: Prisma.SortOrder;
	organizationId?: Prisma.SortOrder;
	role?: Prisma.SortOrder;
	joinedAt?: Prisma.SortOrder;
	updatedAt?: Prisma.SortOrder;
	_count?: Prisma.OrganizationMemberCountOrderByAggregateInput;
	_max?: Prisma.OrganizationMemberMaxOrderByAggregateInput;
	_min?: Prisma.OrganizationMemberMinOrderByAggregateInput;
};

export type OrganizationMemberScalarWhereWithAggregatesInput = {
	AND?:
		| Prisma.OrganizationMemberScalarWhereWithAggregatesInput
		| Prisma.OrganizationMemberScalarWhereWithAggregatesInput[];
	OR?: Prisma.OrganizationMemberScalarWhereWithAggregatesInput[];
	NOT?:
		| Prisma.OrganizationMemberScalarWhereWithAggregatesInput
		| Prisma.OrganizationMemberScalarWhereWithAggregatesInput[];
	id?: Prisma.StringWithAggregatesFilter<"OrganizationMember"> | string;
	userId?: Prisma.StringWithAggregatesFilter<"OrganizationMember"> | string;
	organizationId?:
		| Prisma.StringWithAggregatesFilter<"OrganizationMember">
		| string;
	role?:
		| Prisma.EnumRoleWithAggregatesFilter<"OrganizationMember">
		| $Enums.Role;
	joinedAt?:
		| Prisma.DateTimeWithAggregatesFilter<"OrganizationMember">
		| Date
		| string;
	updatedAt?:
		| Prisma.DateTimeWithAggregatesFilter<"OrganizationMember">
		| Date
		| string;
};

export type OrganizationMemberCreateInput = {
	id?: string;
	role?: $Enums.Role;
	joinedAt?: Date | string;
	updatedAt?: Date | string;
	user: Prisma.UserCreateNestedOneWithoutOrganizationMembershipsInput;
	organization: Prisma.OrganizationCreateNestedOneWithoutMembersInput;
};

export type OrganizationMemberUncheckedCreateInput = {
	id?: string;
	userId: string;
	organizationId: string;
	role?: $Enums.Role;
	joinedAt?: Date | string;
	updatedAt?: Date | string;
};

export type OrganizationMemberUpdateInput = {
	id?: Prisma.StringFieldUpdateOperationsInput | string;
	role?: Prisma.EnumRoleFieldUpdateOperationsInput | $Enums.Role;
	joinedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	user?: Prisma.UserUpdateOneRequiredWithoutOrganizationMembershipsNestedInput;
	organization?: Prisma.OrganizationUpdateOneRequiredWithoutMembersNestedInput;
};

export type OrganizationMemberUncheckedUpdateInput = {
	id?: Prisma.StringFieldUpdateOperationsInput | string;
	userId?: Prisma.StringFieldUpdateOperationsInput | string;
	organizationId?: Prisma.StringFieldUpdateOperationsInput | string;
	role?: Prisma.EnumRoleFieldUpdateOperationsInput | $Enums.Role;
	joinedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
};

export type OrganizationMemberCreateManyInput = {
	id?: string;
	userId: string;
	organizationId: string;
	role?: $Enums.Role;
	joinedAt?: Date | string;
	updatedAt?: Date | string;
};

export type OrganizationMemberUpdateManyMutationInput = {
	id?: Prisma.StringFieldUpdateOperationsInput | string;
	role?: Prisma.EnumRoleFieldUpdateOperationsInput | $Enums.Role;
	joinedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
};

export type OrganizationMemberUncheckedUpdateManyInput = {
	id?: Prisma.StringFieldUpdateOperationsInput | string;
	userId?: Prisma.StringFieldUpdateOperationsInput | string;
	organizationId?: Prisma.StringFieldUpdateOperationsInput | string;
	role?: Prisma.EnumRoleFieldUpdateOperationsInput | $Enums.Role;
	joinedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
};

export type OrganizationMemberListRelationFilter = {
	every?: Prisma.OrganizationMemberWhereInput;
	some?: Prisma.OrganizationMemberWhereInput;
	none?: Prisma.OrganizationMemberWhereInput;
};

export type OrganizationMemberOrderByRelationAggregateInput = {
	_count?: Prisma.SortOrder;
};

export type OrganizationMemberUserIdOrganizationIdCompoundUniqueInput = {
	userId: string;
	organizationId: string;
};

export type OrganizationMemberCountOrderByAggregateInput = {
	id?: Prisma.SortOrder;
	userId?: Prisma.SortOrder;
	organizationId?: Prisma.SortOrder;
	role?: Prisma.SortOrder;
	joinedAt?: Prisma.SortOrder;
	updatedAt?: Prisma.SortOrder;
};

export type OrganizationMemberMaxOrderByAggregateInput = {
	id?: Prisma.SortOrder;
	userId?: Prisma.SortOrder;
	organizationId?: Prisma.SortOrder;
	role?: Prisma.SortOrder;
	joinedAt?: Prisma.SortOrder;
	updatedAt?: Prisma.SortOrder;
};

export type OrganizationMemberMinOrderByAggregateInput = {
	id?: Prisma.SortOrder;
	userId?: Prisma.SortOrder;
	organizationId?: Prisma.SortOrder;
	role?: Prisma.SortOrder;
	joinedAt?: Prisma.SortOrder;
	updatedAt?: Prisma.SortOrder;
};

export type OrganizationMemberCreateNestedManyWithoutUserInput = {
	create?:
		| Prisma.XOR<
				Prisma.OrganizationMemberCreateWithoutUserInput,
				Prisma.OrganizationMemberUncheckedCreateWithoutUserInput
		  >
		| Prisma.OrganizationMemberCreateWithoutUserInput[]
		| Prisma.OrganizationMemberUncheckedCreateWithoutUserInput[];
	connectOrCreate?:
		| Prisma.OrganizationMemberCreateOrConnectWithoutUserInput
		| Prisma.OrganizationMemberCreateOrConnectWithoutUserInput[];
	createMany?: Prisma.OrganizationMemberCreateManyUserInputEnvelope;
	connect?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
};

export type OrganizationMemberUncheckedCreateNestedManyWithoutUserInput = {
	create?:
		| Prisma.XOR<
				Prisma.OrganizationMemberCreateWithoutUserInput,
				Prisma.OrganizationMemberUncheckedCreateWithoutUserInput
		  >
		| Prisma.OrganizationMemberCreateWithoutUserInput[]
		| Prisma.OrganizationMemberUncheckedCreateWithoutUserInput[];
	connectOrCreate?:
		| Prisma.OrganizationMemberCreateOrConnectWithoutUserInput
		| Prisma.OrganizationMemberCreateOrConnectWithoutUserInput[];
	createMany?: Prisma.OrganizationMemberCreateManyUserInputEnvelope;
	connect?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
};

export type OrganizationMemberUpdateManyWithoutUserNestedInput = {
	create?:
		| Prisma.XOR<
				Prisma.OrganizationMemberCreateWithoutUserInput,
				Prisma.OrganizationMemberUncheckedCreateWithoutUserInput
		  >
		| Prisma.OrganizationMemberCreateWithoutUserInput[]
		| Prisma.OrganizationMemberUncheckedCreateWithoutUserInput[];
	connectOrCreate?:
		| Prisma.OrganizationMemberCreateOrConnectWithoutUserInput
		| Prisma.OrganizationMemberCreateOrConnectWithoutUserInput[];
	upsert?:
		| Prisma.OrganizationMemberUpsertWithWhereUniqueWithoutUserInput
		| Prisma.OrganizationMemberUpsertWithWhereUniqueWithoutUserInput[];
	createMany?: Prisma.OrganizationMemberCreateManyUserInputEnvelope;
	set?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
	disconnect?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
	delete?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
	connect?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
	update?:
		| Prisma.OrganizationMemberUpdateWithWhereUniqueWithoutUserInput
		| Prisma.OrganizationMemberUpdateWithWhereUniqueWithoutUserInput[];
	updateMany?:
		| Prisma.OrganizationMemberUpdateManyWithWhereWithoutUserInput
		| Prisma.OrganizationMemberUpdateManyWithWhereWithoutUserInput[];
	deleteMany?:
		| Prisma.OrganizationMemberScalarWhereInput
		| Prisma.OrganizationMemberScalarWhereInput[];
};

export type OrganizationMemberUncheckedUpdateManyWithoutUserNestedInput = {
	create?:
		| Prisma.XOR<
				Prisma.OrganizationMemberCreateWithoutUserInput,
				Prisma.OrganizationMemberUncheckedCreateWithoutUserInput
		  >
		| Prisma.OrganizationMemberCreateWithoutUserInput[]
		| Prisma.OrganizationMemberUncheckedCreateWithoutUserInput[];
	connectOrCreate?:
		| Prisma.OrganizationMemberCreateOrConnectWithoutUserInput
		| Prisma.OrganizationMemberCreateOrConnectWithoutUserInput[];
	upsert?:
		| Prisma.OrganizationMemberUpsertWithWhereUniqueWithoutUserInput
		| Prisma.OrganizationMemberUpsertWithWhereUniqueWithoutUserInput[];
	createMany?: Prisma.OrganizationMemberCreateManyUserInputEnvelope;
	set?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
	disconnect?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
	delete?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
	connect?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
	update?:
		| Prisma.OrganizationMemberUpdateWithWhereUniqueWithoutUserInput
		| Prisma.OrganizationMemberUpdateWithWhereUniqueWithoutUserInput[];
	updateMany?:
		| Prisma.OrganizationMemberUpdateManyWithWhereWithoutUserInput
		| Prisma.OrganizationMemberUpdateManyWithWhereWithoutUserInput[];
	deleteMany?:
		| Prisma.OrganizationMemberScalarWhereInput
		| Prisma.OrganizationMemberScalarWhereInput[];
};

export type OrganizationMemberCreateNestedManyWithoutOrganizationInput = {
	create?:
		| Prisma.XOR<
				Prisma.OrganizationMemberCreateWithoutOrganizationInput,
				Prisma.OrganizationMemberUncheckedCreateWithoutOrganizationInput
		  >
		| Prisma.OrganizationMemberCreateWithoutOrganizationInput[]
		| Prisma.OrganizationMemberUncheckedCreateWithoutOrganizationInput[];
	connectOrCreate?:
		| Prisma.OrganizationMemberCreateOrConnectWithoutOrganizationInput
		| Prisma.OrganizationMemberCreateOrConnectWithoutOrganizationInput[];
	createMany?: Prisma.OrganizationMemberCreateManyOrganizationInputEnvelope;
	connect?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
};

export type OrganizationMemberUncheckedCreateNestedManyWithoutOrganizationInput =
	{
		create?:
			| Prisma.XOR<
					Prisma.OrganizationMemberCreateWithoutOrganizationInput,
					Prisma.OrganizationMemberUncheckedCreateWithoutOrganizationInput
			  >
			| Prisma.OrganizationMemberCreateWithoutOrganizationInput[]
			| Prisma.OrganizationMemberUncheckedCreateWithoutOrganizationInput[];
		connectOrCreate?:
			| Prisma.OrganizationMemberCreateOrConnectWithoutOrganizationInput
			| Prisma.OrganizationMemberCreateOrConnectWithoutOrganizationInput[];
		createMany?: Prisma.OrganizationMemberCreateManyOrganizationInputEnvelope;
		connect?:
			| Prisma.OrganizationMemberWhereUniqueInput
			| Prisma.OrganizationMemberWhereUniqueInput[];
	};

export type OrganizationMemberUpdateManyWithoutOrganizationNestedInput = {
	create?:
		| Prisma.XOR<
				Prisma.OrganizationMemberCreateWithoutOrganizationInput,
				Prisma.OrganizationMemberUncheckedCreateWithoutOrganizationInput
		  >
		| Prisma.OrganizationMemberCreateWithoutOrganizationInput[]
		| Prisma.OrganizationMemberUncheckedCreateWithoutOrganizationInput[];
	connectOrCreate?:
		| Prisma.OrganizationMemberCreateOrConnectWithoutOrganizationInput
		| Prisma.OrganizationMemberCreateOrConnectWithoutOrganizationInput[];
	upsert?:
		| Prisma.OrganizationMemberUpsertWithWhereUniqueWithoutOrganizationInput
		| Prisma.OrganizationMemberUpsertWithWhereUniqueWithoutOrganizationInput[];
	createMany?: Prisma.OrganizationMemberCreateManyOrganizationInputEnvelope;
	set?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
	disconnect?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
	delete?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
	connect?:
		| Prisma.OrganizationMemberWhereUniqueInput
		| Prisma.OrganizationMemberWhereUniqueInput[];
	update?:
		| Prisma.OrganizationMemberUpdateWithWhereUniqueWithoutOrganizationInput
		| Prisma.OrganizationMemberUpdateWithWhereUniqueWithoutOrganizationInput[];
	updateMany?:
		| Prisma.OrganizationMemberUpdateManyWithWhereWithoutOrganizationInput
		| Prisma.OrganizationMemberUpdateManyWithWhereWithoutOrganizationInput[];
	deleteMany?:
		| Prisma.OrganizationMemberScalarWhereInput
		| Prisma.OrganizationMemberScalarWhereInput[];
};

export type OrganizationMemberUncheckedUpdateManyWithoutOrganizationNestedInput =
	{
		create?:
			| Prisma.XOR<
					Prisma.OrganizationMemberCreateWithoutOrganizationInput,
					Prisma.OrganizationMemberUncheckedCreateWithoutOrganizationInput
			  >
			| Prisma.OrganizationMemberCreateWithoutOrganizationInput[]
			| Prisma.OrganizationMemberUncheckedCreateWithoutOrganizationInput[];
		connectOrCreate?:
			| Prisma.OrganizationMemberCreateOrConnectWithoutOrganizationInput
			| Prisma.OrganizationMemberCreateOrConnectWithoutOrganizationInput[];
		upsert?:
			| Prisma.OrganizationMemberUpsertWithWhereUniqueWithoutOrganizationInput
			| Prisma.OrganizationMemberUpsertWithWhereUniqueWithoutOrganizationInput[];
		createMany?: Prisma.OrganizationMemberCreateManyOrganizationInputEnvelope;
		set?:
			| Prisma.OrganizationMemberWhereUniqueInput
			| Prisma.OrganizationMemberWhereUniqueInput[];
		disconnect?:
			| Prisma.OrganizationMemberWhereUniqueInput
			| Prisma.OrganizationMemberWhereUniqueInput[];
		delete?:
			| Prisma.OrganizationMemberWhereUniqueInput
			| Prisma.OrganizationMemberWhereUniqueInput[];
		connect?:
			| Prisma.OrganizationMemberWhereUniqueInput
			| Prisma.OrganizationMemberWhereUniqueInput[];
		update?:
			| Prisma.OrganizationMemberUpdateWithWhereUniqueWithoutOrganizationInput
			| Prisma.OrganizationMemberUpdateWithWhereUniqueWithoutOrganizationInput[];
		updateMany?:
			| Prisma.OrganizationMemberUpdateManyWithWhereWithoutOrganizationInput
			| Prisma.OrganizationMemberUpdateManyWithWhereWithoutOrganizationInput[];
		deleteMany?:
			| Prisma.OrganizationMemberScalarWhereInput
			| Prisma.OrganizationMemberScalarWhereInput[];
	};

export type EnumRoleFieldUpdateOperationsInput = {
	set?: $Enums.Role;
};

export type OrganizationMemberCreateWithoutUserInput = {
	id?: string;
	role?: $Enums.Role;
	joinedAt?: Date | string;
	updatedAt?: Date | string;
	organization: Prisma.OrganizationCreateNestedOneWithoutMembersInput;
};

export type OrganizationMemberUncheckedCreateWithoutUserInput = {
	id?: string;
	organizationId: string;
	role?: $Enums.Role;
	joinedAt?: Date | string;
	updatedAt?: Date | string;
};

export type OrganizationMemberCreateOrConnectWithoutUserInput = {
	where: Prisma.OrganizationMemberWhereUniqueInput;
	create: Prisma.XOR<
		Prisma.OrganizationMemberCreateWithoutUserInput,
		Prisma.OrganizationMemberUncheckedCreateWithoutUserInput
	>;
};

export type OrganizationMemberCreateManyUserInputEnvelope = {
	data:
		| Prisma.OrganizationMemberCreateManyUserInput
		| Prisma.OrganizationMemberCreateManyUserInput[];
	skipDuplicates?: boolean;
};

export type OrganizationMemberUpsertWithWhereUniqueWithoutUserInput = {
	where: Prisma.OrganizationMemberWhereUniqueInput;
	update: Prisma.XOR<
		Prisma.OrganizationMemberUpdateWithoutUserInput,
		Prisma.OrganizationMemberUncheckedUpdateWithoutUserInput
	>;
	create: Prisma.XOR<
		Prisma.OrganizationMemberCreateWithoutUserInput,
		Prisma.OrganizationMemberUncheckedCreateWithoutUserInput
	>;
};

export type OrganizationMemberUpdateWithWhereUniqueWithoutUserInput = {
	where: Prisma.OrganizationMemberWhereUniqueInput;
	data: Prisma.XOR<
		Prisma.OrganizationMemberUpdateWithoutUserInput,
		Prisma.OrganizationMemberUncheckedUpdateWithoutUserInput
	>;
};

export type OrganizationMemberUpdateManyWithWhereWithoutUserInput = {
	where: Prisma.OrganizationMemberScalarWhereInput;
	data: Prisma.XOR<
		Prisma.OrganizationMemberUpdateManyMutationInput,
		Prisma.OrganizationMemberUncheckedUpdateManyWithoutUserInput
	>;
};

export type OrganizationMemberScalarWhereInput = {
	AND?:
		| Prisma.OrganizationMemberScalarWhereInput
		| Prisma.OrganizationMemberScalarWhereInput[];
	OR?: Prisma.OrganizationMemberScalarWhereInput[];
	NOT?:
		| Prisma.OrganizationMemberScalarWhereInput
		| Prisma.OrganizationMemberScalarWhereInput[];
	id?: Prisma.StringFilter<"OrganizationMember"> | string;
	userId?: Prisma.StringFilter<"OrganizationMember"> | string;
	organizationId?: Prisma.StringFilter<"OrganizationMember"> | string;
	role?: Prisma.EnumRoleFilter<"OrganizationMember"> | $Enums.Role;
	joinedAt?: Prisma.DateTimeFilter<"OrganizationMember"> | Date | string;
	updatedAt?: Prisma.DateTimeFilter<"OrganizationMember"> | Date | string;
};

export type OrganizationMemberCreateWithoutOrganizationInput = {
	id?: string;
	role?: $Enums.Role;
	joinedAt?: Date | string;
	updatedAt?: Date | string;
	user: Prisma.UserCreateNestedOneWithoutOrganizationMembershipsInput;
};

export type OrganizationMemberUncheckedCreateWithoutOrganizationInput = {
	id?: string;
	userId: string;
	role?: $Enums.Role;
	joinedAt?: Date | string;
	updatedAt?: Date | string;
};

export type OrganizationMemberCreateOrConnectWithoutOrganizationInput = {
	where: Prisma.OrganizationMemberWhereUniqueInput;
	create: Prisma.XOR<
		Prisma.OrganizationMemberCreateWithoutOrganizationInput,
		Prisma.OrganizationMemberUncheckedCreateWithoutOrganizationInput
	>;
};

export type OrganizationMemberCreateManyOrganizationInputEnvelope = {
	data:
		| Prisma.OrganizationMemberCreateManyOrganizationInput
		| Prisma.OrganizationMemberCreateManyOrganizationInput[];
	skipDuplicates?: boolean;
};

export type OrganizationMemberUpsertWithWhereUniqueWithoutOrganizationInput = {
	where: Prisma.OrganizationMemberWhereUniqueInput;
	update: Prisma.XOR<
		Prisma.OrganizationMemberUpdateWithoutOrganizationInput,
		Prisma.OrganizationMemberUncheckedUpdateWithoutOrganizationInput
	>;
	create: Prisma.XOR<
		Prisma.OrganizationMemberCreateWithoutOrganizationInput,
		Prisma.OrganizationMemberUncheckedCreateWithoutOrganizationInput
	>;
};

export type OrganizationMemberUpdateWithWhereUniqueWithoutOrganizationInput = {
	where: Prisma.OrganizationMemberWhereUniqueInput;
	data: Prisma.XOR<
		Prisma.OrganizationMemberUpdateWithoutOrganizationInput,
		Prisma.OrganizationMemberUncheckedUpdateWithoutOrganizationInput
	>;
};

export type OrganizationMemberUpdateManyWithWhereWithoutOrganizationInput = {
	where: Prisma.OrganizationMemberScalarWhereInput;
	data: Prisma.XOR<
		Prisma.OrganizationMemberUpdateManyMutationInput,
		Prisma.OrganizationMemberUncheckedUpdateManyWithoutOrganizationInput
	>;
};

export type OrganizationMemberCreateManyUserInput = {
	id?: string;
	organizationId: string;
	role?: $Enums.Role;
	joinedAt?: Date | string;
	updatedAt?: Date | string;
};

export type OrganizationMemberUpdateWithoutUserInput = {
	id?: Prisma.StringFieldUpdateOperationsInput | string;
	role?: Prisma.EnumRoleFieldUpdateOperationsInput | $Enums.Role;
	joinedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	organization?: Prisma.OrganizationUpdateOneRequiredWithoutMembersNestedInput;
};

export type OrganizationMemberUncheckedUpdateWithoutUserInput = {
	id?: Prisma.StringFieldUpdateOperationsInput | string;
	organizationId?: Prisma.StringFieldUpdateOperationsInput | string;
	role?: Prisma.EnumRoleFieldUpdateOperationsInput | $Enums.Role;
	joinedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
};

export type OrganizationMemberUncheckedUpdateManyWithoutUserInput = {
	id?: Prisma.StringFieldUpdateOperationsInput | string;
	organizationId?: Prisma.StringFieldUpdateOperationsInput | string;
	role?: Prisma.EnumRoleFieldUpdateOperationsInput | $Enums.Role;
	joinedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
};

export type OrganizationMemberCreateManyOrganizationInput = {
	id?: string;
	userId: string;
	role?: $Enums.Role;
	joinedAt?: Date | string;
	updatedAt?: Date | string;
};

export type OrganizationMemberUpdateWithoutOrganizationInput = {
	id?: Prisma.StringFieldUpdateOperationsInput | string;
	role?: Prisma.EnumRoleFieldUpdateOperationsInput | $Enums.Role;
	joinedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	user?: Prisma.UserUpdateOneRequiredWithoutOrganizationMembershipsNestedInput;
};

export type OrganizationMemberUncheckedUpdateWithoutOrganizationInput = {
	id?: Prisma.StringFieldUpdateOperationsInput | string;
	userId?: Prisma.StringFieldUpdateOperationsInput | string;
	role?: Prisma.EnumRoleFieldUpdateOperationsInput | $Enums.Role;
	joinedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
};

export type OrganizationMemberUncheckedUpdateManyWithoutOrganizationInput = {
	id?: Prisma.StringFieldUpdateOperationsInput | string;
	userId?: Prisma.StringFieldUpdateOperationsInput | string;
	role?: Prisma.EnumRoleFieldUpdateOperationsInput | $Enums.Role;
	joinedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
};

export type OrganizationMemberSelect<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
	{
		id?: boolean;
		userId?: boolean;
		organizationId?: boolean;
		role?: boolean;
		joinedAt?: boolean;
		updatedAt?: boolean;
		user?: boolean | Prisma.UserDefaultArgs<ExtArgs>;
		organization?: boolean | Prisma.OrganizationDefaultArgs<ExtArgs>;
	},
	ExtArgs["result"]["organizationMember"]
>;

export type OrganizationMemberSelectCreateManyAndReturn<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
	{
		id?: boolean;
		userId?: boolean;
		organizationId?: boolean;
		role?: boolean;
		joinedAt?: boolean;
		updatedAt?: boolean;
		user?: boolean | Prisma.UserDefaultArgs<ExtArgs>;
		organization?: boolean | Prisma.OrganizationDefaultArgs<ExtArgs>;
	},
	ExtArgs["result"]["organizationMember"]
>;

export type OrganizationMemberSelectUpdateManyAndReturn<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
	{
		id?: boolean;
		userId?: boolean;
		organizationId?: boolean;
		role?: boolean;
		joinedAt?: boolean;
		updatedAt?: boolean;
		user?: boolean | Prisma.UserDefaultArgs<ExtArgs>;
		organization?: boolean | Prisma.OrganizationDefaultArgs<ExtArgs>;
	},
	ExtArgs["result"]["organizationMember"]
>;

export type OrganizationMemberSelectScalar = {
	id?: boolean;
	userId?: boolean;
	organizationId?: boolean;
	role?: boolean;
	joinedAt?: boolean;
	updatedAt?: boolean;
};

export type OrganizationMemberOmit<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
	"id" | "userId" | "organizationId" | "role" | "joinedAt" | "updatedAt",
	ExtArgs["result"]["organizationMember"]
>;
export type OrganizationMemberInclude<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	user?: boolean | Prisma.UserDefaultArgs<ExtArgs>;
	organization?: boolean | Prisma.OrganizationDefaultArgs<ExtArgs>;
};
export type OrganizationMemberIncludeCreateManyAndReturn<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	user?: boolean | Prisma.UserDefaultArgs<ExtArgs>;
	organization?: boolean | Prisma.OrganizationDefaultArgs<ExtArgs>;
};
export type OrganizationMemberIncludeUpdateManyAndReturn<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	user?: boolean | Prisma.UserDefaultArgs<ExtArgs>;
	organization?: boolean | Prisma.OrganizationDefaultArgs<ExtArgs>;
};

export type $OrganizationMemberPayload<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	name: "OrganizationMember";
	objects: {
		user: Prisma.$UserPayload<ExtArgs>;
		organization: Prisma.$OrganizationPayload<ExtArgs>;
	};
	scalars: runtime.Types.Extensions.GetPayloadResult<
		{
			id: string;
			userId: string;
			organizationId: string;
			role: $Enums.Role;
			joinedAt: Date;
			updatedAt: Date;
		},
		ExtArgs["result"]["organizationMember"]
	>;
	composites: {};
};

export type OrganizationMemberGetPayload<
	S extends boolean | null | undefined | OrganizationMemberDefaultArgs,
> = runtime.Types.Result.GetResult<Prisma.$OrganizationMemberPayload, S>;

export type OrganizationMemberCountArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<
	OrganizationMemberFindManyArgs,
	"select" | "include" | "distinct" | "omit"
> & {
	select?: OrganizationMemberCountAggregateInputType | true;
};

export interface OrganizationMemberDelegate<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
	GlobalOmitOptions = {},
> {
	[K: symbol]: {
		types: Prisma.TypeMap<ExtArgs>["model"]["OrganizationMember"];
		meta: { name: "OrganizationMember" };
	};
	/**
	 * Find zero or one OrganizationMember that matches the filter.
	 * @param {OrganizationMemberFindUniqueArgs} args - Arguments to find a OrganizationMember
	 * @example
	 * // Get one OrganizationMember
	 * const organizationMember = await prisma.organizationMember.findUnique({
	 *   where: {
	 *     // ... provide filter here
	 *   }
	 * })
	 */
	findUnique<T extends OrganizationMemberFindUniqueArgs>(
		args: Prisma.SelectSubset<T, OrganizationMemberFindUniqueArgs<ExtArgs>>,
	): Prisma.Prisma__OrganizationMemberClient<
		runtime.Types.Result.GetResult<
			Prisma.$OrganizationMemberPayload<ExtArgs>,
			T,
			"findUnique",
			GlobalOmitOptions
		> | null,
		null,
		ExtArgs,
		GlobalOmitOptions
	>;

	/**
	 * Find one OrganizationMember that matches the filter or throw an error with `error.code='P2025'`
	 * if no matches were found.
	 * @param {OrganizationMemberFindUniqueOrThrowArgs} args - Arguments to find a OrganizationMember
	 * @example
	 * // Get one OrganizationMember
	 * const organizationMember = await prisma.organizationMember.findUniqueOrThrow({
	 *   where: {
	 *     // ... provide filter here
	 *   }
	 * })
	 */
	findUniqueOrThrow<T extends OrganizationMemberFindUniqueOrThrowArgs>(
		args: Prisma.SelectSubset<
			T,
			OrganizationMemberFindUniqueOrThrowArgs<ExtArgs>
		>,
	): Prisma.Prisma__OrganizationMemberClient<
		runtime.Types.Result.GetResult<
			Prisma.$OrganizationMemberPayload<ExtArgs>,
			T,
			"findUniqueOrThrow",
			GlobalOmitOptions
		>,
		never,
		ExtArgs,
		GlobalOmitOptions
	>;

	/**
	 * Find the first OrganizationMember that matches the filter.
	 * Note, that providing `undefined` is treated as the value not being there.
	 * Read more here: https://pris.ly/d/null-undefined
	 * @param {OrganizationMemberFindFirstArgs} args - Arguments to find a OrganizationMember
	 * @example
	 * // Get one OrganizationMember
	 * const organizationMember = await prisma.organizationMember.findFirst({
	 *   where: {
	 *     // ... provide filter here
	 *   }
	 * })
	 */
	findFirst<T extends OrganizationMemberFindFirstArgs>(
		args?: Prisma.SelectSubset<T, OrganizationMemberFindFirstArgs<ExtArgs>>,
	): Prisma.Prisma__OrganizationMemberClient<
		runtime.Types.Result.GetResult<
			Prisma.$OrganizationMemberPayload<ExtArgs>,
			T,
			"findFirst",
			GlobalOmitOptions
		> | null,
		null,
		ExtArgs,
		GlobalOmitOptions
	>;

	/**
	 * Find the first OrganizationMember that matches the filter or
	 * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
	 * Note, that providing `undefined` is treated as the value not being there.
	 * Read more here: https://pris.ly/d/null-undefined
	 * @param {OrganizationMemberFindFirstOrThrowArgs} args - Arguments to find a OrganizationMember
	 * @example
	 * // Get one OrganizationMember
	 * const organizationMember = await prisma.organizationMember.findFirstOrThrow({
	 *   where: {
	 *     // ... provide filter here
	 *   }
	 * })
	 */
	findFirstOrThrow<T extends OrganizationMemberFindFirstOrThrowArgs>(
		args?: Prisma.SelectSubset<
			T,
			OrganizationMemberFindFirstOrThrowArgs<ExtArgs>
		>,
	): Prisma.Prisma__OrganizationMemberClient<
		runtime.Types.Result.GetResult<
			Prisma.$OrganizationMemberPayload<ExtArgs>,
			T,
			"findFirstOrThrow",
			GlobalOmitOptions
		>,
		never,
		ExtArgs,
		GlobalOmitOptions
	>;

	/**
	 * Find zero or more OrganizationMembers that matches the filter.
	 * Note, that providing `undefined` is treated as the value not being there.
	 * Read more here: https://pris.ly/d/null-undefined
	 * @param {OrganizationMemberFindManyArgs} args - Arguments to filter and select certain fields only.
	 * @example
	 * // Get all OrganizationMembers
	 * const organizationMembers = await prisma.organizationMember.findMany()
	 *
	 * // Get first 10 OrganizationMembers
	 * const organizationMembers = await prisma.organizationMember.findMany({ take: 10 })
	 *
	 * // Only select the `id`
	 * const organizationMemberWithIdOnly = await prisma.organizationMember.findMany({ select: { id: true } })
	 *
	 */
	findMany<T extends OrganizationMemberFindManyArgs>(
		args?: Prisma.SelectSubset<T, OrganizationMemberFindManyArgs<ExtArgs>>,
	): Prisma.PrismaPromise<
		runtime.Types.Result.GetResult<
			Prisma.$OrganizationMemberPayload<ExtArgs>,
			T,
			"findMany",
			GlobalOmitOptions
		>
	>;

	/**
	 * Create a OrganizationMember.
	 * @param {OrganizationMemberCreateArgs} args - Arguments to create a OrganizationMember.
	 * @example
	 * // Create one OrganizationMember
	 * const OrganizationMember = await prisma.organizationMember.create({
	 *   data: {
	 *     // ... data to create a OrganizationMember
	 *   }
	 * })
	 *
	 */
	create<T extends OrganizationMemberCreateArgs>(
		args: Prisma.SelectSubset<T, OrganizationMemberCreateArgs<ExtArgs>>,
	): Prisma.Prisma__OrganizationMemberClient<
		runtime.Types.Result.GetResult<
			Prisma.$OrganizationMemberPayload<ExtArgs>,
			T,
			"create",
			GlobalOmitOptions
		>,
		never,
		ExtArgs,
		GlobalOmitOptions
	>;

	/**
	 * Create many OrganizationMembers.
	 * @param {OrganizationMemberCreateManyArgs} args - Arguments to create many OrganizationMembers.
	 * @example
	 * // Create many OrganizationMembers
	 * const organizationMember = await prisma.organizationMember.createMany({
	 *   data: [
	 *     // ... provide data here
	 *   ]
	 * })
	 *
	 */
	createMany<T extends OrganizationMemberCreateManyArgs>(
		args?: Prisma.SelectSubset<T, OrganizationMemberCreateManyArgs<ExtArgs>>,
	): Prisma.PrismaPromise<Prisma.BatchPayload>;

	/**
	 * Create many OrganizationMembers and returns the data saved in the database.
	 * @param {OrganizationMemberCreateManyAndReturnArgs} args - Arguments to create many OrganizationMembers.
	 * @example
	 * // Create many OrganizationMembers
	 * const organizationMember = await prisma.organizationMember.createManyAndReturn({
	 *   data: [
	 *     // ... provide data here
	 *   ]
	 * })
	 *
	 * // Create many OrganizationMembers and only return the `id`
	 * const organizationMemberWithIdOnly = await prisma.organizationMember.createManyAndReturn({
	 *   select: { id: true },
	 *   data: [
	 *     // ... provide data here
	 *   ]
	 * })
	 * Note, that providing `undefined` is treated as the value not being there.
	 * Read more here: https://pris.ly/d/null-undefined
	 *
	 */
	createManyAndReturn<T extends OrganizationMemberCreateManyAndReturnArgs>(
		args?: Prisma.SelectSubset<
			T,
			OrganizationMemberCreateManyAndReturnArgs<ExtArgs>
		>,
	): Prisma.PrismaPromise<
		runtime.Types.Result.GetResult<
			Prisma.$OrganizationMemberPayload<ExtArgs>,
			T,
			"createManyAndReturn",
			GlobalOmitOptions
		>
	>;

	/**
	 * Delete a OrganizationMember.
	 * @param {OrganizationMemberDeleteArgs} args - Arguments to delete one OrganizationMember.
	 * @example
	 * // Delete one OrganizationMember
	 * const OrganizationMember = await prisma.organizationMember.delete({
	 *   where: {
	 *     // ... filter to delete one OrganizationMember
	 *   }
	 * })
	 *
	 */
	delete<T extends OrganizationMemberDeleteArgs>(
		args: Prisma.SelectSubset<T, OrganizationMemberDeleteArgs<ExtArgs>>,
	): Prisma.Prisma__OrganizationMemberClient<
		runtime.Types.Result.GetResult<
			Prisma.$OrganizationMemberPayload<ExtArgs>,
			T,
			"delete",
			GlobalOmitOptions
		>,
		never,
		ExtArgs,
		GlobalOmitOptions
	>;

	/**
	 * Update one OrganizationMember.
	 * @param {OrganizationMemberUpdateArgs} args - Arguments to update one OrganizationMember.
	 * @example
	 * // Update one OrganizationMember
	 * const organizationMember = await prisma.organizationMember.update({
	 *   where: {
	 *     // ... provide filter here
	 *   },
	 *   data: {
	 *     // ... provide data here
	 *   }
	 * })
	 *
	 */
	update<T extends OrganizationMemberUpdateArgs>(
		args: Prisma.SelectSubset<T, OrganizationMemberUpdateArgs<ExtArgs>>,
	): Prisma.Prisma__OrganizationMemberClient<
		runtime.Types.Result.GetResult<
			Prisma.$OrganizationMemberPayload<ExtArgs>,
			T,
			"update",
			GlobalOmitOptions
		>,
		never,
		ExtArgs,
		GlobalOmitOptions
	>;

	/**
	 * Delete zero or more OrganizationMembers.
	 * @param {OrganizationMemberDeleteManyArgs} args - Arguments to filter OrganizationMembers to delete.
	 * @example
	 * // Delete a few OrganizationMembers
	 * const { count } = await prisma.organizationMember.deleteMany({
	 *   where: {
	 *     // ... provide filter here
	 *   }
	 * })
	 *
	 */
	deleteMany<T extends OrganizationMemberDeleteManyArgs>(
		args?: Prisma.SelectSubset<T, OrganizationMemberDeleteManyArgs<ExtArgs>>,
	): Prisma.PrismaPromise<Prisma.BatchPayload>;

	/**
	 * Update zero or more OrganizationMembers.
	 * Note, that providing `undefined` is treated as the value not being there.
	 * Read more here: https://pris.ly/d/null-undefined
	 * @param {OrganizationMemberUpdateManyArgs} args - Arguments to update one or more rows.
	 * @example
	 * // Update many OrganizationMembers
	 * const organizationMember = await prisma.organizationMember.updateMany({
	 *   where: {
	 *     // ... provide filter here
	 *   },
	 *   data: {
	 *     // ... provide data here
	 *   }
	 * })
	 *
	 */
	updateMany<T extends OrganizationMemberUpdateManyArgs>(
		args: Prisma.SelectSubset<T, OrganizationMemberUpdateManyArgs<ExtArgs>>,
	): Prisma.PrismaPromise<Prisma.BatchPayload>;

	/**
	 * Update zero or more OrganizationMembers and returns the data updated in the database.
	 * @param {OrganizationMemberUpdateManyAndReturnArgs} args - Arguments to update many OrganizationMembers.
	 * @example
	 * // Update many OrganizationMembers
	 * const organizationMember = await prisma.organizationMember.updateManyAndReturn({
	 *   where: {
	 *     // ... provide filter here
	 *   },
	 *   data: [
	 *     // ... provide data here
	 *   ]
	 * })
	 *
	 * // Update zero or more OrganizationMembers and only return the `id`
	 * const organizationMemberWithIdOnly = await prisma.organizationMember.updateManyAndReturn({
	 *   select: { id: true },
	 *   where: {
	 *     // ... provide filter here
	 *   },
	 *   data: [
	 *     // ... provide data here
	 *   ]
	 * })
	 * Note, that providing `undefined` is treated as the value not being there.
	 * Read more here: https://pris.ly/d/null-undefined
	 *
	 */
	updateManyAndReturn<T extends OrganizationMemberUpdateManyAndReturnArgs>(
		args: Prisma.SelectSubset<
			T,
			OrganizationMemberUpdateManyAndReturnArgs<ExtArgs>
		>,
	): Prisma.PrismaPromise<
		runtime.Types.Result.GetResult<
			Prisma.$OrganizationMemberPayload<ExtArgs>,
			T,
			"updateManyAndReturn",
			GlobalOmitOptions
		>
	>;

	/**
	 * Create or update one OrganizationMember.
	 * @param {OrganizationMemberUpsertArgs} args - Arguments to update or create a OrganizationMember.
	 * @example
	 * // Update or create a OrganizationMember
	 * const organizationMember = await prisma.organizationMember.upsert({
	 *   create: {
	 *     // ... data to create a OrganizationMember
	 *   },
	 *   update: {
	 *     // ... in case it already exists, update
	 *   },
	 *   where: {
	 *     // ... the filter for the OrganizationMember we want to update
	 *   }
	 * })
	 */
	upsert<T extends OrganizationMemberUpsertArgs>(
		args: Prisma.SelectSubset<T, OrganizationMemberUpsertArgs<ExtArgs>>,
	): Prisma.Prisma__OrganizationMemberClient<
		runtime.Types.Result.GetResult<
			Prisma.$OrganizationMemberPayload<ExtArgs>,
			T,
			"upsert",
			GlobalOmitOptions
		>,
		never,
		ExtArgs,
		GlobalOmitOptions
	>;

	/**
	 * Count the number of OrganizationMembers.
	 * Note, that providing `undefined` is treated as the value not being there.
	 * Read more here: https://pris.ly/d/null-undefined
	 * @param {OrganizationMemberCountArgs} args - Arguments to filter OrganizationMembers to count.
	 * @example
	 * // Count the number of OrganizationMembers
	 * const count = await prisma.organizationMember.count({
	 *   where: {
	 *     // ... the filter for the OrganizationMembers we want to count
	 *   }
	 * })
	 **/
	count<T extends OrganizationMemberCountArgs>(
		args?: Prisma.Subset<T, OrganizationMemberCountArgs>,
	): Prisma.PrismaPromise<
		T extends runtime.Types.Utils.Record<"select", any>
			? T["select"] extends true
				? number
				: Prisma.GetScalarType<
						T["select"],
						OrganizationMemberCountAggregateOutputType
					>
			: number
	>;

	/**
	 * Allows you to perform aggregations operations on a OrganizationMember.
	 * Note, that providing `undefined` is treated as the value not being there.
	 * Read more here: https://pris.ly/d/null-undefined
	 * @param {OrganizationMemberAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
	 * @example
	 * // Ordered by age ascending
	 * // Where email contains prisma.io
	 * // Limited to the 10 users
	 * const aggregations = await prisma.user.aggregate({
	 *   _avg: {
	 *     age: true,
	 *   },
	 *   where: {
	 *     email: {
	 *       contains: "prisma.io",
	 *     },
	 *   },
	 *   orderBy: {
	 *     age: "asc",
	 *   },
	 *   take: 10,
	 * })
	 **/
	aggregate<T extends OrganizationMemberAggregateArgs>(
		args: Prisma.Subset<T, OrganizationMemberAggregateArgs>,
	): Prisma.PrismaPromise<GetOrganizationMemberAggregateType<T>>;

	/**
	 * Group by OrganizationMember.
	 * Note, that providing `undefined` is treated as the value not being there.
	 * Read more here: https://pris.ly/d/null-undefined
	 * @param {OrganizationMemberGroupByArgs} args - Group by arguments.
	 * @example
	 * // Group by city, order by createdAt, get count
	 * const result = await prisma.user.groupBy({
	 *   by: ['city', 'createdAt'],
	 *   orderBy: {
	 *     createdAt: true
	 *   },
	 *   _count: {
	 *     _all: true
	 *   },
	 * })
	 *
	 **/
	groupBy<
		T extends OrganizationMemberGroupByArgs,
		HasSelectOrTake extends Prisma.Or<
			Prisma.Extends<"skip", Prisma.Keys<T>>,
			Prisma.Extends<"take", Prisma.Keys<T>>
		>,
		OrderByArg extends Prisma.True extends HasSelectOrTake
			? { orderBy: OrganizationMemberGroupByArgs["orderBy"] }
			: { orderBy?: OrganizationMemberGroupByArgs["orderBy"] },
		OrderFields extends Prisma.ExcludeUnderscoreKeys<
			Prisma.Keys<Prisma.MaybeTupleToUnion<T["orderBy"]>>
		>,
		ByFields extends Prisma.MaybeTupleToUnion<T["by"]>,
		ByValid extends Prisma.Has<ByFields, OrderFields>,
		HavingFields extends Prisma.GetHavingFields<T["having"]>,
		HavingValid extends Prisma.Has<ByFields, HavingFields>,
		ByEmpty extends T["by"] extends never[] ? Prisma.True : Prisma.False,
		InputErrors extends ByEmpty extends Prisma.True
			? `Error: "by" must not be empty.`
			: HavingValid extends Prisma.False
				? {
						[P in HavingFields]: P extends ByFields
							? never
							: P extends string
								? `Error: Field "${P}" used in "having" needs to be provided in "by".`
								: [
										Error,
										"Field ",
										P,
										` in "having" needs to be provided in "by"`,
									];
					}[HavingFields]
				: "take" extends Prisma.Keys<T>
					? "orderBy" extends Prisma.Keys<T>
						? ByValid extends Prisma.True
							? {}
							: {
									[P in OrderFields]: P extends ByFields
										? never
										: `Error: Field "${P}" in "orderBy" needs to be provided in "by"`;
								}[OrderFields]
						: 'Error: If you provide "take", you also need to provide "orderBy"'
					: "skip" extends Prisma.Keys<T>
						? "orderBy" extends Prisma.Keys<T>
							? ByValid extends Prisma.True
								? {}
								: {
										[P in OrderFields]: P extends ByFields
											? never
											: `Error: Field "${P}" in "orderBy" needs to be provided in "by"`;
									}[OrderFields]
							: 'Error: If you provide "skip", you also need to provide "orderBy"'
						: ByValid extends Prisma.True
							? {}
							: {
									[P in OrderFields]: P extends ByFields
										? never
										: `Error: Field "${P}" in "orderBy" needs to be provided in "by"`;
								}[OrderFields],
	>(
		args: Prisma.SubsetIntersection<
			T,
			OrganizationMemberGroupByArgs,
			OrderByArg
		> &
			InputErrors,
	): {} extends InputErrors
		? GetOrganizationMemberGroupByPayload<T>
		: Prisma.PrismaPromise<InputErrors>;
	/**
	 * Fields of the OrganizationMember model
	 */
	readonly fields: OrganizationMemberFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for OrganizationMember.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__OrganizationMemberClient<
	T,
	Null = never,
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
	GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
	readonly [Symbol.toStringTag]: "PrismaPromise";
	user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(
		args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>,
	): Prisma.Prisma__UserClient<
		| runtime.Types.Result.GetResult<
				Prisma.$UserPayload<ExtArgs>,
				T,
				"findUniqueOrThrow",
				GlobalOmitOptions
		  >
		| Null,
		Null,
		ExtArgs,
		GlobalOmitOptions
	>;
	organization<T extends Prisma.OrganizationDefaultArgs<ExtArgs> = {}>(
		args?: Prisma.Subset<T, Prisma.OrganizationDefaultArgs<ExtArgs>>,
	): Prisma.Prisma__OrganizationClient<
		| runtime.Types.Result.GetResult<
				Prisma.$OrganizationPayload<ExtArgs>,
				T,
				"findUniqueOrThrow",
				GlobalOmitOptions
		  >
		| Null,
		Null,
		ExtArgs,
		GlobalOmitOptions
	>;
	/**
	 * Attaches callbacks for the resolution and/or rejection of the Promise.
	 * @param onfulfilled The callback to execute when the Promise is resolved.
	 * @param onrejected The callback to execute when the Promise is rejected.
	 * @returns A Promise for the completion of which ever callback is executed.
	 */
	then<TResult1 = T, TResult2 = never>(
		onfulfilled?:
			| ((value: T) => TResult1 | PromiseLike<TResult1>)
			| undefined
			| null,
		onrejected?:
			| ((reason: any) => TResult2 | PromiseLike<TResult2>)
			| undefined
			| null,
	): runtime.Types.Utils.JsPromise<TResult1 | TResult2>;
	/**
	 * Attaches a callback for only the rejection of the Promise.
	 * @param onrejected The callback to execute when the Promise is rejected.
	 * @returns A Promise for the completion of the callback.
	 */
	catch<TResult = never>(
		onrejected?:
			| ((reason: any) => TResult | PromiseLike<TResult>)
			| undefined
			| null,
	): runtime.Types.Utils.JsPromise<T | TResult>;
	/**
	 * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
	 * resolved value cannot be modified from the callback.
	 * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
	 * @returns A Promise for the completion of the callback.
	 */
	finally(
		onfinally?: (() => void) | undefined | null,
	): runtime.Types.Utils.JsPromise<T>;
}

/**
 * Fields of the OrganizationMember model
 */
export interface OrganizationMemberFieldRefs {
	readonly id: Prisma.FieldRef<"OrganizationMember", "String">;
	readonly userId: Prisma.FieldRef<"OrganizationMember", "String">;
	readonly organizationId: Prisma.FieldRef<"OrganizationMember", "String">;
	readonly role: Prisma.FieldRef<"OrganizationMember", "Role">;
	readonly joinedAt: Prisma.FieldRef<"OrganizationMember", "DateTime">;
	readonly updatedAt: Prisma.FieldRef<"OrganizationMember", "DateTime">;
}

// Custom InputTypes
/**
 * OrganizationMember findUnique
 */
export type OrganizationMemberFindUniqueArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Select specific fields to fetch from the OrganizationMember
	 */
	select?: Prisma.OrganizationMemberSelect<ExtArgs> | null;
	/**
	 * Omit specific fields from the OrganizationMember
	 */
	omit?: Prisma.OrganizationMemberOmit<ExtArgs> | null;
	/**
	 * Choose, which related nodes to fetch as well
	 */
	include?: Prisma.OrganizationMemberInclude<ExtArgs> | null;
	/**
	 * Filter, which OrganizationMember to fetch.
	 */
	where: Prisma.OrganizationMemberWhereUniqueInput;
};

/**
 * OrganizationMember findUniqueOrThrow
 */
export type OrganizationMemberFindUniqueOrThrowArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Select specific fields to fetch from the OrganizationMember
	 */
	select?: Prisma.OrganizationMemberSelect<ExtArgs> | null;
	/**
	 * Omit specific fields from the OrganizationMember
	 */
	omit?: Prisma.OrganizationMemberOmit<ExtArgs> | null;
	/**
	 * Choose, which related nodes to fetch as well
	 */
	include?: Prisma.OrganizationMemberInclude<ExtArgs> | null;
	/**
	 * Filter, which OrganizationMember to fetch.
	 */
	where: Prisma.OrganizationMemberWhereUniqueInput;
};

/**
 * OrganizationMember findFirst
 */
export type OrganizationMemberFindFirstArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Select specific fields to fetch from the OrganizationMember
	 */
	select?: Prisma.OrganizationMemberSelect<ExtArgs> | null;
	/**
	 * Omit specific fields from the OrganizationMember
	 */
	omit?: Prisma.OrganizationMemberOmit<ExtArgs> | null;
	/**
	 * Choose, which related nodes to fetch as well
	 */
	include?: Prisma.OrganizationMemberInclude<ExtArgs> | null;
	/**
	 * Filter, which OrganizationMember to fetch.
	 */
	where?: Prisma.OrganizationMemberWhereInput;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
	 *
	 * Determine the order of OrganizationMembers to fetch.
	 */
	orderBy?:
		| Prisma.OrganizationMemberOrderByWithRelationInput
		| Prisma.OrganizationMemberOrderByWithRelationInput[];
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
	 *
	 * Sets the position for searching for OrganizationMembers.
	 */
	cursor?: Prisma.OrganizationMemberWhereUniqueInput;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
	 *
	 * Take `±n` OrganizationMembers from the position of the cursor.
	 */
	take?: number;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
	 *
	 * Skip the first `n` OrganizationMembers.
	 */
	skip?: number;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
	 *
	 * Filter by unique combinations of OrganizationMembers.
	 */
	distinct?:
		| Prisma.OrganizationMemberScalarFieldEnum
		| Prisma.OrganizationMemberScalarFieldEnum[];
};

/**
 * OrganizationMember findFirstOrThrow
 */
export type OrganizationMemberFindFirstOrThrowArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Select specific fields to fetch from the OrganizationMember
	 */
	select?: Prisma.OrganizationMemberSelect<ExtArgs> | null;
	/**
	 * Omit specific fields from the OrganizationMember
	 */
	omit?: Prisma.OrganizationMemberOmit<ExtArgs> | null;
	/**
	 * Choose, which related nodes to fetch as well
	 */
	include?: Prisma.OrganizationMemberInclude<ExtArgs> | null;
	/**
	 * Filter, which OrganizationMember to fetch.
	 */
	where?: Prisma.OrganizationMemberWhereInput;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
	 *
	 * Determine the order of OrganizationMembers to fetch.
	 */
	orderBy?:
		| Prisma.OrganizationMemberOrderByWithRelationInput
		| Prisma.OrganizationMemberOrderByWithRelationInput[];
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
	 *
	 * Sets the position for searching for OrganizationMembers.
	 */
	cursor?: Prisma.OrganizationMemberWhereUniqueInput;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
	 *
	 * Take `±n` OrganizationMembers from the position of the cursor.
	 */
	take?: number;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
	 *
	 * Skip the first `n` OrganizationMembers.
	 */
	skip?: number;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
	 *
	 * Filter by unique combinations of OrganizationMembers.
	 */
	distinct?:
		| Prisma.OrganizationMemberScalarFieldEnum
		| Prisma.OrganizationMemberScalarFieldEnum[];
};

/**
 * OrganizationMember findMany
 */
export type OrganizationMemberFindManyArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Select specific fields to fetch from the OrganizationMember
	 */
	select?: Prisma.OrganizationMemberSelect<ExtArgs> | null;
	/**
	 * Omit specific fields from the OrganizationMember
	 */
	omit?: Prisma.OrganizationMemberOmit<ExtArgs> | null;
	/**
	 * Choose, which related nodes to fetch as well
	 */
	include?: Prisma.OrganizationMemberInclude<ExtArgs> | null;
	/**
	 * Filter, which OrganizationMembers to fetch.
	 */
	where?: Prisma.OrganizationMemberWhereInput;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
	 *
	 * Determine the order of OrganizationMembers to fetch.
	 */
	orderBy?:
		| Prisma.OrganizationMemberOrderByWithRelationInput
		| Prisma.OrganizationMemberOrderByWithRelationInput[];
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
	 *
	 * Sets the position for listing OrganizationMembers.
	 */
	cursor?: Prisma.OrganizationMemberWhereUniqueInput;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
	 *
	 * Take `±n` OrganizationMembers from the position of the cursor.
	 */
	take?: number;
	/**
	 * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
	 *
	 * Skip the first `n` OrganizationMembers.
	 */
	skip?: number;
	distinct?:
		| Prisma.OrganizationMemberScalarFieldEnum
		| Prisma.OrganizationMemberScalarFieldEnum[];
};

/**
 * OrganizationMember create
 */
export type OrganizationMemberCreateArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Select specific fields to fetch from the OrganizationMember
	 */
	select?: Prisma.OrganizationMemberSelect<ExtArgs> | null;
	/**
	 * Omit specific fields from the OrganizationMember
	 */
	omit?: Prisma.OrganizationMemberOmit<ExtArgs> | null;
	/**
	 * Choose, which related nodes to fetch as well
	 */
	include?: Prisma.OrganizationMemberInclude<ExtArgs> | null;
	/**
	 * The data needed to create a OrganizationMember.
	 */
	data: Prisma.XOR<
		Prisma.OrganizationMemberCreateInput,
		Prisma.OrganizationMemberUncheckedCreateInput
	>;
};

/**
 * OrganizationMember createMany
 */
export type OrganizationMemberCreateManyArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * The data used to create many OrganizationMembers.
	 */
	data:
		| Prisma.OrganizationMemberCreateManyInput
		| Prisma.OrganizationMemberCreateManyInput[];
	skipDuplicates?: boolean;
};

/**
 * OrganizationMember createManyAndReturn
 */
export type OrganizationMemberCreateManyAndReturnArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Select specific fields to fetch from the OrganizationMember
	 */
	select?: Prisma.OrganizationMemberSelectCreateManyAndReturn<ExtArgs> | null;
	/**
	 * Omit specific fields from the OrganizationMember
	 */
	omit?: Prisma.OrganizationMemberOmit<ExtArgs> | null;
	/**
	 * The data used to create many OrganizationMembers.
	 */
	data:
		| Prisma.OrganizationMemberCreateManyInput
		| Prisma.OrganizationMemberCreateManyInput[];
	skipDuplicates?: boolean;
	/**
	 * Choose, which related nodes to fetch as well
	 */
	include?: Prisma.OrganizationMemberIncludeCreateManyAndReturn<ExtArgs> | null;
};

/**
 * OrganizationMember update
 */
export type OrganizationMemberUpdateArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Select specific fields to fetch from the OrganizationMember
	 */
	select?: Prisma.OrganizationMemberSelect<ExtArgs> | null;
	/**
	 * Omit specific fields from the OrganizationMember
	 */
	omit?: Prisma.OrganizationMemberOmit<ExtArgs> | null;
	/**
	 * Choose, which related nodes to fetch as well
	 */
	include?: Prisma.OrganizationMemberInclude<ExtArgs> | null;
	/**
	 * The data needed to update a OrganizationMember.
	 */
	data: Prisma.XOR<
		Prisma.OrganizationMemberUpdateInput,
		Prisma.OrganizationMemberUncheckedUpdateInput
	>;
	/**
	 * Choose, which OrganizationMember to update.
	 */
	where: Prisma.OrganizationMemberWhereUniqueInput;
};

/**
 * OrganizationMember updateMany
 */
export type OrganizationMemberUpdateManyArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * The data used to update OrganizationMembers.
	 */
	data: Prisma.XOR<
		Prisma.OrganizationMemberUpdateManyMutationInput,
		Prisma.OrganizationMemberUncheckedUpdateManyInput
	>;
	/**
	 * Filter which OrganizationMembers to update
	 */
	where?: Prisma.OrganizationMemberWhereInput;
	/**
	 * Limit how many OrganizationMembers to update.
	 */
	limit?: number;
};

/**
 * OrganizationMember updateManyAndReturn
 */
export type OrganizationMemberUpdateManyAndReturnArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Select specific fields to fetch from the OrganizationMember
	 */
	select?: Prisma.OrganizationMemberSelectUpdateManyAndReturn<ExtArgs> | null;
	/**
	 * Omit specific fields from the OrganizationMember
	 */
	omit?: Prisma.OrganizationMemberOmit<ExtArgs> | null;
	/**
	 * The data used to update OrganizationMembers.
	 */
	data: Prisma.XOR<
		Prisma.OrganizationMemberUpdateManyMutationInput,
		Prisma.OrganizationMemberUncheckedUpdateManyInput
	>;
	/**
	 * Filter which OrganizationMembers to update
	 */
	where?: Prisma.OrganizationMemberWhereInput;
	/**
	 * Limit how many OrganizationMembers to update.
	 */
	limit?: number;
	/**
	 * Choose, which related nodes to fetch as well
	 */
	include?: Prisma.OrganizationMemberIncludeUpdateManyAndReturn<ExtArgs> | null;
};

/**
 * OrganizationMember upsert
 */
export type OrganizationMemberUpsertArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Select specific fields to fetch from the OrganizationMember
	 */
	select?: Prisma.OrganizationMemberSelect<ExtArgs> | null;
	/**
	 * Omit specific fields from the OrganizationMember
	 */
	omit?: Prisma.OrganizationMemberOmit<ExtArgs> | null;
	/**
	 * Choose, which related nodes to fetch as well
	 */
	include?: Prisma.OrganizationMemberInclude<ExtArgs> | null;
	/**
	 * The filter to search for the OrganizationMember to update in case it exists.
	 */
	where: Prisma.OrganizationMemberWhereUniqueInput;
	/**
	 * In case the OrganizationMember found by the `where` argument doesn't exist, create a new OrganizationMember with this data.
	 */
	create: Prisma.XOR<
		Prisma.OrganizationMemberCreateInput,
		Prisma.OrganizationMemberUncheckedCreateInput
	>;
	/**
	 * In case the OrganizationMember was found with the provided `where` argument, update it with this data.
	 */
	update: Prisma.XOR<
		Prisma.OrganizationMemberUpdateInput,
		Prisma.OrganizationMemberUncheckedUpdateInput
	>;
};

/**
 * OrganizationMember delete
 */
export type OrganizationMemberDeleteArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Select specific fields to fetch from the OrganizationMember
	 */
	select?: Prisma.OrganizationMemberSelect<ExtArgs> | null;
	/**
	 * Omit specific fields from the OrganizationMember
	 */
	omit?: Prisma.OrganizationMemberOmit<ExtArgs> | null;
	/**
	 * Choose, which related nodes to fetch as well
	 */
	include?: Prisma.OrganizationMemberInclude<ExtArgs> | null;
	/**
	 * Filter which OrganizationMember to delete.
	 */
	where: Prisma.OrganizationMemberWhereUniqueInput;
};

/**
 * OrganizationMember deleteMany
 */
export type OrganizationMemberDeleteManyArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Filter which OrganizationMembers to delete
	 */
	where?: Prisma.OrganizationMemberWhereInput;
	/**
	 * Limit how many OrganizationMembers to delete.
	 */
	limit?: number;
};

/**
 * OrganizationMember without action
 */
export type OrganizationMemberDefaultArgs<
	ExtArgs extends
		runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
	/**
	 * Select specific fields to fetch from the OrganizationMember
	 */
	select?: Prisma.OrganizationMemberSelect<ExtArgs> | null;
	/**
	 * Omit specific fields from the OrganizationMember
	 */
	omit?: Prisma.OrganizationMemberOmit<ExtArgs> | null;
	/**
	 * Choose, which related nodes to fetch as well
	 */
	include?: Prisma.OrganizationMemberInclude<ExtArgs> | null;
};
