/**
 * Comprehensive usage examples for Prisma utilities
 * This file demonstrates how to use the prisma-helpers functions
 * with the existing organization, invitation, and user management systems
 */

import prisma from "../../prisma";
import {
  paginateQuery,
  paginateWithCursor,
  executeQuery,
  searchRecords,
  countRecords,
  aggregateRecords,
  createDateRangeFilter,
  createNumberRangeFilter,
  createSearchParams,
  createSortParams,
  combineFilters,
  sanitizeSearchTerm,
  type PagePaginationParams,
  type CursorPaginationParams,
  type SearchParams,
  type FilterParams,
  type QueryParams,
} from "../lib/prisma-helpers";

// ===== ORGANIZATION EXAMPLES =====

/**
 * Example 1: Basic page-based pagination for organizations
 */
export async function getOrganizationsWithPagination(page = 1, limit = 10) {
  return await paginateQuery({
    model: prisma.organization,
    page,
    limit,
    orderBy: { createdAt: 'desc' },
    include: {
      members: {
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
        },
      },
    },
  });
}

/**
 * Example 2: Search organizations with filters
 */
export async function searchOrganizations(searchTerm: string, organizationType?: string) {
  const search = createSearchParams(['name', 'description'], sanitizeSearchTerm(searchTerm));
  
  const filters: FilterParams = organizationType 
    ? { type: organizationType }
    : {};

  return await paginateQuery({
    model: prisma.organization,
    page: 1,
    limit: 20,
    search,
    filters,
    orderBy: { name: 'asc' },
  });
}

/**
 * Example 3: Advanced filtering with date ranges
 */
export async function getRecentOrganizations(days = 30) {
  const fromDate = new Date();
  fromDate.setDate(fromDate.getDate() - days);

  const dateFilter = createDateRangeFilter('createdAt', fromDate);
  const typeFilter = { type: 'TEAM' };
  
  const combinedFilters = combineFilters(dateFilter, typeFilter);

  return await paginateQuery({
    model: prisma.organization,
    page: 1,
    limit: 50,
    filters: combinedFilters,
    orderBy: { createdAt: 'desc' },
  });
}

/**
 * Example 4: Cursor-based pagination for large datasets
 */
export async function getOrganizationsWithCursor(cursor?: string, take = 20) {
  return await paginateWithCursor({
    model: prisma.organization,
    cursor,
    take,
    cursorField: 'id',
    direction: 'forward',
    orderBy: { createdAt: 'desc' },
    include: {
      _count: {
        select: { members: true },
      },
    },
  });
}

// ===== USER EXAMPLES =====

/**
 * Example 5: Search users with complex filters
 */
export async function searchUsersAdvanced(params: {
  searchTerm?: string;
  organizationId?: string;
  joinedAfter?: Date;
  page?: number;
  limit?: number;
}) {
  const { searchTerm, organizationId, joinedAfter, page = 1, limit = 10 } = params;

  let search: SearchParams | undefined;
  if (searchTerm) {
    search = createSearchParams(['name', 'email'], sanitizeSearchTerm(searchTerm));
  }

  let filters: FilterParams = {};
  if (organizationId) {
    filters.organizationMembers = {
      some: { organizationId },
    };
  }
  if (joinedAfter) {
    const dateFilter = createDateRangeFilter('createdAt', joinedAfter);
    filters = combineFilters(filters, dateFilter);
  }

  return await paginateQuery({
    model: prisma.user,
    page,
    limit,
    search,
    filters,
    orderBy: { createdAt: 'desc' },
    include: {
      organizationMembers: {
        include: {
          organization: {
            select: { id: true, name: true, type: true },
          },
        },
      },
    },
  });
}

// ===== INVITATION EXAMPLES =====

/**
 * Example 6: Get invitations with status filtering
 */
export async function getInvitationsByStatus(
  status: 'PENDING' | 'ACCEPTED' | 'EXPIRED' | 'REVOKED',
  organizationId?: string
) {
  const filters: FilterParams = { status };
  if (organizationId) {
    filters.organizationId = organizationId;
  }

  return await paginateQuery({
    model: prisma.invitation,
    page: 1,
    limit: 25,
    filters,
    orderBy: { createdAt: 'desc' },
    include: {
      organization: {
        select: { id: true, name: true },
      },
      invitedBy: {
        select: { id: true, name: true, email: true },
      },
    },
  });
}

// ===== AGGREGATION EXAMPLES =====

/**
 * Example 7: Get organization statistics
 */
export async function getOrganizationStats() {
  return await aggregateRecords({
    model: prisma.organization,
    aggregations: {
      _count: true,
      _min: { createdAt: true },
      _max: { createdAt: true },
    },
    groupBy: ['type'],
  });
}

/**
 * Example 8: Count active organizations
 */
export async function countActiveOrganizations() {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const filters = createDateRangeFilter('lastActivityAt', thirtyDaysAgo);

  return await countRecords({
    model: prisma.organization,
    filters,
  });
}

// ===== COMBINED QUERY EXAMPLES =====

/**
 * Example 9: Using the unified executeQuery function
 */
export async function getOrganizationsUnified(queryParams: QueryParams) {
  return await executeQuery({
    model: prisma.organization,
    queryParams,
  });
}

/**
 * Example 10: Complex search without pagination
 */
export async function findSimilarOrganizations(name: string, limit = 10) {
  const search = createSearchParams(['name', 'description'], sanitizeSearchTerm(name));

  return await searchRecords({
    model: prisma.organization,
    search,
    orderBy: { name: 'asc' },
    limit,
    select: {
      id: true,
      name: true,
      description: true,
      type: true,
      createdAt: true,
    },
  });
}

// ===== USAGE IN ROUTE HANDLERS =====

/**
 * Example 11: How to use in a Hono route handler
 */
export function createOrganizationRouteExample() {
  // This would be used in your actual route files
  return async (c: any) => {
    try {
      // Extract query parameters
      const page = Number(c.req.query('page')) || 1;
      const limit = Number(c.req.query('limit')) || 10;
      const search = c.req.query('search');
      const type = c.req.query('type');
      const sortBy = c.req.query('sortBy') || 'createdAt';
      const sortOrder = c.req.query('sortOrder') || 'desc';

      // Build query parameters
      const queryParams: QueryParams = {
        pagination: { page, limit },
        orderBy: { [sortBy]: sortOrder },
      };

      if (search) {
        queryParams.search = createSearchParams(['name', 'description'], sanitizeSearchTerm(search));
      }

      if (type) {
        queryParams.filters = { type };
      }

      // Execute query
      const result = await executeQuery({
        model: prisma.organization,
        queryParams,
      });

      return c.json(result);
    } catch (error) {
      console.error('Organization query error:', error);
      return c.json({ error: 'Failed to fetch organizations' }, 500);
    }
  };
}

// ===== EXPORT USAGE EXAMPLES =====

export const PrismaHelpersExamples = {
  getOrganizationsWithPagination,
  searchOrganizations,
  getRecentOrganizations,
  getOrganizationsWithCursor,
  searchUsersAdvanced,
  getInvitationsByStatus,
  getOrganizationStats,
  countActiveOrganizations,
  getOrganizationsUnified,
  findSimilarOrganizations,
  createOrganizationRouteExample,
};
