# Enhanced Organization Endpoints with Prisma Utilities

This document demonstrates the enhanced organization management endpoints that now use the comprehensive Prisma utilities for advanced querying capabilities.

## 🚀 Enhanced Endpoints

### 1. **GET /api/organizations** - Advanced Organization Listing

**Features:**
- ✅ **Pagination** with page and limit parameters
- ✅ **Search** across organization name and description
- ✅ **Filtering** by organization type and creation date
- ✅ **Sorting** by any field with asc/desc order
- ✅ **Metadata** with pagination info (totalCount, hasNext, etc.)

**Query Parameters:**
```typescript
{
  page?: number;           // Page number (default: 1)
  limit?: number;          // Items per page (default: 10, max: 50)
  search?: string;         // Search term for name/description
  type?: "PERSONAL" | "TEAM" | "ENTERPRISE"; // Filter by org type
  sortBy?: string;         // Field to sort by (default: "createdAt")
  sortOrder?: "asc" | "desc"; // Sort direction (default: "desc")
  createdAfter?: string;   // ISO date string for date filtering
}
```

**Example Requests:**
```bash
# Basic pagination
GET /api/organizations?page=1&limit=20

# Search with filtering
GET /api/organizations?search=acme&type=TEAM&sortBy=name&sortOrder=asc

# Date filtering
GET /api/organizations?createdAfter=2024-01-01T00:00:00Z&limit=50
```

**Response Format:**
```json
{
  "data": [
    {
      "id": "org_123",
      "name": "Acme Corp",
      "slug": "acme-corp",
      "description": "A great company",
      "type": "TEAM",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-20T14:45:00Z",
      "role": "ADMIN",
      "memberCount": 5
    }
  ],
  "meta": {
    "currentPage": 1,
    "totalPages": 3,
    "totalCount": 25,
    "limit": 10,
    "hasNext": true,
    "hasPrevious": false
  }
}
```

### 2. **GET /api/organizations/search** - Dedicated Search Endpoint

**Features:**
- ✅ **Fast text search** across name and description fields
- ✅ **Type filtering** for refined results
- ✅ **Optimized response** with only essential fields
- ✅ **Search term sanitization** for security

**Query Parameters:**
```typescript
{
  q: string;               // Required search term
  limit?: number;          // Max results (default: 20, max: 50)
  type?: "PERSONAL" | "TEAM" | "ENTERPRISE"; // Filter by org type
}
```

**Example Requests:**
```bash
# Basic search
GET /api/organizations/search?q=marketing

# Search with type filter
GET /api/organizations/search?q=team&type=TEAM&limit=10
```

**Response Format:**
```json
{
  "results": [
    {
      "id": "org_456",
      "name": "Marketing Team",
      "slug": "marketing-team",
      "description": "Our marketing department",
      "type": "TEAM",
      "createdAt": "2024-01-10T09:15:00Z"
    }
  ],
  "count": 3,
  "searchTerm": "marketing"
}
```

### 3. **GET /api/organizations/stats** - Organization Analytics

**Features:**
- ✅ **Total organization count** for the user
- ✅ **Recent activity tracking** with configurable time window
- ✅ **Type breakdown** showing distribution by organization type
- ✅ **Parallel query execution** for optimal performance

**Query Parameters:**
```typescript
{
  days?: number;           // Time window for "recent" stats (default: 30)
}
```

**Example Requests:**
```bash
# Default 30-day stats
GET /api/organizations/stats

# Custom time window
GET /api/organizations/stats?days=7
```

**Response Format:**
```json
{
  "total": 12,
  "recent": 3,
  "recentDays": 30,
  "byType": {
    "PERSONAL": 8,
    "TEAM": 4,
    "ENTERPRISE": 0
  }
}
```

## 🛠️ Implementation Highlights

### **Prisma Utilities Used:**

1. **Search Utilities:**
   ```typescript
   const searchParams = createSearchParams(
     ["name", "description"],
     sanitizeSearchTerm(searchTerm)
   );
   ```

2. **Filter Utilities:**
   ```typescript
   const dateFilter = createDateRangeFilter("createdAt", fromDate);
   const combinedFilters = combineFilters(baseFilter, dateFilter);
   ```

3. **Input Sanitization:**
   ```typescript
   const cleanTerm = sanitizeSearchTerm(userInput);
   ```

### **Performance Optimizations:**

1. **Parallel Queries:**
   ```typescript
   const [organizations, totalCount] = await Promise.all([
     prisma.organization.findMany(queryOptions),
     prisma.organization.count({ where: whereConditions }),
   ]);
   ```

2. **Query Limits:**
   ```typescript
   const limit = Math.min(Number(c.req.query("limit")) || 10, 50);
   ```

3. **Selective Field Loading:**
   ```typescript
   select: {
     id: true,
     name: true,
     slug: true,
     // Only load needed fields
   }
   ```

## 🔒 Security Features

1. **Input Sanitization:** All search terms are sanitized to prevent injection attacks
2. **Access Control:** Users can only see organizations they're members of
3. **Rate Limiting:** Built-in limits prevent resource exhaustion
4. **Type Safety:** Full TypeScript coverage prevents runtime errors

## 📊 Backward Compatibility

The enhanced endpoints maintain backward compatibility:
- ✅ **Default behavior** unchanged when no query parameters provided
- ✅ **Response structure** includes original data format
- ✅ **Existing clients** continue to work without modifications
- ✅ **Progressive enhancement** - new features are opt-in via query parameters

## 🎯 Usage in Frontend Applications

```typescript
// React/Next.js example
const useOrganizations = (params: OrganizationQueryParams) => {
  return useSWR(
    `/api/organizations?${new URLSearchParams(params)}`,
    fetcher
  );
};

// Usage
const { data, error, isLoading } = useOrganizations({
  page: 1,
  limit: 20,
  search: "marketing",
  type: "TEAM"
});

// Access pagination metadata
const { data: organizations, meta } = data;
console.log(`Showing ${organizations.length} of ${meta.totalCount} organizations`);
```

This implementation showcases how the comprehensive Prisma utilities enhance the existing organization management system with advanced querying capabilities while maintaining clean, type-safe, and performant code.
