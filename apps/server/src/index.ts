import "dotenv/config";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { auth } from "./lib/auth";
import { initializeBackgroundJobs } from "./lib/background-jobs";
import adminRouter from "./routes/admin";
import invitationsRouter from "./routes/invitations";
import onboardingRouter from "./routes/onboarding";
import organizationsRouter from "./routes/organizations";
import sessionRouter from "./routes/session";

const app = new Hono();

app.use(logger());
app.use(
	"/*",
	cors({
		origin: process.env.CORS_ORIGIN || "",
		allowMethods: ["GET", "POST", "OPTIONS"],
		allowHeaders: ["Content-Type", "Authorization"],
		credentials: true,
	}),
);

app.on(["POST", "GET"], "/api/auth/**", (c) => auth.handler(c.req.raw));

// API routes
app.route("/api/admin", adminRouter);
app.route("/api/organizations", organizationsRouter);
app.route("/api/invitations", invitationsRouter);
app.route("/api/onboarding", onboardingRouter);
app.route("/api/session", sessionRouter);

app.get("/", (c) => {
	return c.text("OK");
});

// Initialize background jobs
initializeBackgroundJobs();

export default app;
