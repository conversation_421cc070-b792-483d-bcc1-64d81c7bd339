import type { Prisma } from "prisma/generated/client";

// ===== TYPE DEFINITIONS =====

/**
 * Generic Prisma model delegate type
 */
export type PrismaModelDelegate<T = Record<string, unknown>> = {
	findMany: (args?: PrismaQueryOptions) => Promise<T[]>;
	count: (args?: { where?: Record<string, unknown> }) => Promise<number>;
	findFirst: (args?: PrismaQueryOptions) => Promise<T | null>;
	findUnique: (args?: PrismaQueryOptions) => Promise<T | null>;
};

/**
 * Prisma query options type
 */
export type PrismaQueryOptions = {
	where?: Record<string, unknown>;
	select?: Record<string, boolean | Record<string, unknown>>;
	include?: Record<string, boolean | Record<string, unknown>>;
	orderBy?: unknown; // More flexible to handle various orderBy formats
	skip?: number;
	take?: number;
	cursor?: Record<string, unknown>;
};

/**
 * Generic pagination parameters for page-based pagination
 */
export interface PagePaginationParams {
	page?: number;
	limit?: number;
}

/**
 * Generic pagination parameters for cursor-based pagination
 */
export interface CursorPaginationParams<T = unknown> {
	cursor?: T;
	take?: number;
	skip?: number;
	direction?: "forward" | "backward";
}

/**
 * Search parameters for text-based search
 */
export interface SearchParams {
	fields: string[];
	term: string;
	mode?: "insensitive" | "default";
}

/**
 * Generic filter parameters
 */
export type FilterParams<T = Record<string, unknown>> = Partial<T> & {
	dateRange?: {
		field: string;
		from?: Date;
		to?: Date;
	};
	numberRange?: {
		field: string;
		min?: number;
		max?: number;
	};
};

/**
 * Sort parameters with support for multiple fields
 */
export type SortParams<T = Record<string, unknown>> =
	| { [K in keyof T]?: "asc" | "desc" }
	| Array<{ [K in keyof T]?: "asc" | "desc" }>;

/**
 * Page-based pagination response metadata
 */
export interface PagePaginationMeta {
	currentPage: number;
	totalPages: number;
	totalCount: number;
	limit: number;
	hasNext: boolean;
	hasPrevious: boolean;
}

/**
 * Cursor-based pagination response metadata
 */
export interface CursorPaginationMeta<T = unknown> {
	totalCount?: number;
	hasNext: boolean;
	hasPrevious: boolean;
	nextCursor?: T;
	previousCursor?: T;
	take: number;
}

/**
 * Generic paginated response format
 */
export interface PaginatedResponse<T, M = PagePaginationMeta> {
	data: T[];
	meta: M;
}

/**
 * Combined query parameters for complex queries
 */
export interface QueryParams<
	T = Record<string, unknown>,
	F = Record<string, unknown>,
> {
	// Pagination (choose one)
	pagination?: PagePaginationParams | CursorPaginationParams<unknown>;
	// Search
	search?: SearchParams;
	// Filtering
	filters?: FilterParams<F>;
	// Sorting
	orderBy?: SortParams<T>;
	// Prisma-specific options
	include?: Record<string, boolean | Record<string, unknown>>;
	select?: Record<string, boolean | Record<string, unknown>>;
}

// ===== UTILITY FUNCTIONS =====

/**
 * Validates and normalizes pagination parameters
 */
export function validatePaginationParams(
	params: PagePaginationParams,
): Required<PagePaginationParams> {
	const page = Math.max(1, params.page || 1);
	const limit = Math.min(Math.max(1, params.limit || 10), 100); // Max 100 items per page

	return { page, limit };
}

/**
 * Validates and normalizes cursor pagination parameters
 */
export function validateCursorPaginationParams<T>(
	params: CursorPaginationParams<T>,
): Required<Omit<CursorPaginationParams<T>, "cursor">> & { cursor?: T } {
	const take = Math.min(Math.max(1, params.take || 10), 100); // Max 100 items per page
	const skip = Math.max(0, params.skip || 0);
	const direction = params.direction || "forward";

	return {
		cursor: params.cursor,
		take,
		skip,
		direction,
	};
}

/**
 * Builds search conditions for Prisma queries
 */
export function buildSearchConditions(
	search: SearchParams,
): Record<string, unknown> {
	if (!search.term.trim()) return {};

	const searchConditions = search.fields.map((field) => ({
		[field]: {
			contains: search.term,
			mode: search.mode || "insensitive",
		},
	}));

	return searchConditions.length > 1
		? { OR: searchConditions }
		: searchConditions[0] || {};
}

/**
 * Builds filter conditions for Prisma queries
 */
export function buildFilterConditions<T>(
	filters: FilterParams<T>,
): Record<string, unknown> {
	const conditions: Record<string, unknown> = {};

	// Extract special filter types
	const { dateRange, numberRange, ...regularFilters } = filters;

	// Add regular filters
	Object.entries(regularFilters).forEach(([key, value]) => {
		if (value !== undefined && value !== null && value !== "") {
			conditions[key] = value;
		}
	});

	// Add date range filter
	if (dateRange) {
		const dateCondition: Record<string, Date> = {};
		if (dateRange.from) dateCondition.gte = dateRange.from;
		if (dateRange.to) dateCondition.lte = dateRange.to;

		if (Object.keys(dateCondition).length > 0) {
			conditions[dateRange.field] = dateCondition;
		}
	}

	// Add number range filter
	if (numberRange) {
		const numberCondition: Record<string, number> = {};
		if (numberRange.min !== undefined) numberCondition.gte = numberRange.min;
		if (numberRange.max !== undefined) numberCondition.lte = numberRange.max;

		if (Object.keys(numberCondition).length > 0) {
			conditions[numberRange.field] = numberCondition;
		}
	}

	return conditions;
}

/**
 * Builds sort conditions for Prisma queries
 */
export function buildSortConditions<T>(orderBy?: SortParams<T>): unknown {
	if (!orderBy) return undefined;

	if (Array.isArray(orderBy)) {
		return orderBy;
	}

	return orderBy;
}

/**
 * Combines search, filter, and sort conditions
 */
export function buildWhereConditions<F>(
	search?: SearchParams,
	filters?: FilterParams<F>,
): Record<string, unknown> {
	const searchConditions = search ? buildSearchConditions(search) : {};
	const filterConditions = filters ? buildFilterConditions(filters) : {};

	// Combine conditions
	const hasSearch = Object.keys(searchConditions).length > 0;
	const hasFilters = Object.keys(filterConditions).length > 0;

	if (hasSearch && hasFilters) {
		return {
			AND: [searchConditions, filterConditions],
		};
	}

	if (hasSearch) return searchConditions;
	if (hasFilters) return filterConditions;

	return {};
}

// ===== PAGINATION FUNCTIONS =====

/**
 * Page-based pagination utility
 */
export async function paginateQuery<T, F = Record<string, unknown>>(params: {
	model: PrismaModelDelegate<T>;
	page?: number;
	limit?: number;
	search?: SearchParams;
	filters?: FilterParams<F>;
	orderBy?: SortParams<T>;
	include?: Record<string, boolean | Record<string, unknown>>;
	select?: Record<string, boolean | Record<string, unknown>>;
}): Promise<PaginatedResponse<T, PagePaginationMeta>> {
	const { model, search, filters, orderBy, include, select } = params;
	const { page, limit } = validatePaginationParams({
		page: params.page,
		limit: params.limit,
	});

	// Build query conditions
	const where = buildWhereConditions(search, filters);
	const orderByCondition = buildSortConditions(orderBy);

	// Calculate offset
	const skip = (page - 1) * limit;

	// Build query options
	const queryOptions: PrismaQueryOptions = {
		where,
		skip,
		take: limit,
		orderBy: orderByCondition,
	};

	if (include) queryOptions.include = include;
	if (select) queryOptions.select = select;

	try {
		// Execute queries in parallel
		const [data, totalCount] = await Promise.all([
			model.findMany(queryOptions),
			model.count({ where }),
		]);

		// Calculate pagination metadata
		const totalPages = Math.ceil(totalCount / limit);
		const hasNext = page < totalPages;
		const hasPrevious = page > 1;

		return {
			data,
			meta: {
				currentPage: page,
				totalPages,
				totalCount,
				limit,
				hasNext,
				hasPrevious,
			},
		};
	} catch (error) {
		throw new Error(
			`Pagination query failed: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

/**
 * Cursor-based pagination utility
 */
export async function paginateWithCursor<
	T,
	F = Record<string, unknown>,
>(params: {
	model: PrismaModelDelegate<T>;
	cursor?: unknown;
	take?: number;
	skip?: number;
	direction?: "forward" | "backward";
	cursorField: string; // Field to use for cursor (e.g., 'id', 'createdAt')
	search?: SearchParams;
	filters?: FilterParams<F>;
	orderBy?: SortParams<T>;
	include?: Record<string, boolean | Record<string, unknown>>;
	select?: Record<string, boolean | Record<string, unknown>>;
}): Promise<PaginatedResponse<T, CursorPaginationMeta>> {
	const { model, cursorField, search, filters, orderBy, include, select } =
		params;

	const { cursor, take, skip, direction } = validateCursorPaginationParams({
		cursor: params.cursor,
		take: params.take,
		skip: params.skip,
		direction: params.direction,
	});

	// Build query conditions
	const where = buildWhereConditions(search, filters);
	const orderByCondition = buildSortConditions(orderBy) || {
		[cursorField]: "asc",
	};

	// Build cursor condition
	const cursorCondition = cursor ? { [cursorField]: cursor } : undefined;

	// Build query options
	const queryOptions: PrismaQueryOptions = {
		where,
		take: direction === "backward" ? -take : take,
		skip: skip > 0 ? skip : cursor ? 1 : 0, // Skip cursor itself if provided
		cursor: cursorCondition,
		orderBy: orderByCondition,
	};

	if (include) queryOptions.include = include;
	if (select) queryOptions.select = select;

	try {
		// Execute query
		const data = await model.findMany(queryOptions);

		// Determine pagination metadata
		const hasNext = data.length === take;
		const hasPrevious = cursor !== undefined || skip > 0;

		// Get cursor values
		const nextCursor =
			data.length > 0
				? (data[data.length - 1] as Record<string, unknown>)[cursorField]
				: undefined;
		const previousCursor =
			data.length > 0
				? (data[0] as Record<string, unknown>)[cursorField]
				: undefined;

		return {
			data,
			meta: {
				hasNext,
				hasPrevious,
				nextCursor,
				previousCursor,
				take,
			},
		};
	} catch (error) {
		throw new Error(
			`Cursor pagination query failed: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

// ===== ADVANCED UTILITY FUNCTIONS =====

/**
 * Combined query utility that supports both pagination types
 */
export async function executeQuery<T, F = Record<string, unknown>>(params: {
	model: PrismaModelDelegate<T>;
	queryParams: QueryParams<T, F>;
}): Promise<PaginatedResponse<T, PagePaginationMeta | CursorPaginationMeta>> {
	const { model, queryParams } = params;
	const { pagination, search, filters, orderBy, include, select } = queryParams;

	if (!pagination) {
		// Default to page-based pagination
		return paginateQuery({
			model,
			page: 1,
			limit: 10,
			search,
			filters,
			orderBy,
			include,
			select,
		});
	}

	// Check if it's cursor-based pagination
	if ("cursor" in pagination || "direction" in pagination) {
		const cursorParams = pagination as CursorPaginationParams;
		return paginateWithCursor({
			model,
			cursor: cursorParams.cursor,
			take: cursorParams.take,
			skip: cursorParams.skip,
			direction: cursorParams.direction,
			cursorField: "id", // Default cursor field, should be configurable
			search,
			filters,
			orderBy,
			include,
			select,
		});
	}

	// Page-based pagination
	const pageParams = pagination as PagePaginationParams;
	return paginateQuery({
		model,
		page: pageParams.page,
		limit: pageParams.limit,
		search,
		filters,
		orderBy,
		include,
		select,
	});
}

/**
 * Search-only utility without pagination
 */
export async function searchRecords<T, F = Record<string, unknown>>(params: {
	model: PrismaModelDelegate<T>;
	search: SearchParams;
	filters?: FilterParams<F>;
	orderBy?: SortParams<T>;
	limit?: number;
	include?: Record<string, boolean | Record<string, unknown>>;
	select?: Record<string, boolean | Record<string, unknown>>;
}): Promise<T[]> {
	const {
		model,
		search,
		filters,
		orderBy,
		limit = 50,
		include,
		select,
	} = params;

	const where = buildWhereConditions(search, filters);
	const orderByCondition = buildSortConditions(orderBy);

	const queryOptions: PrismaQueryOptions = {
		where,
		take: Math.min(limit, 100), // Max 100 results
		orderBy: orderByCondition,
	};

	if (include) queryOptions.include = include;
	if (select) queryOptions.select = select;

	try {
		return await model.findMany(queryOptions);
	} catch (error) {
		throw new Error(
			`Search query failed: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

/**
 * Count records with filters and search
 */
export async function countRecords<F = Record<string, unknown>>(params: {
	model: PrismaModelDelegate;
	search?: SearchParams;
	filters?: FilterParams<F>;
}): Promise<number> {
	const { model, search, filters } = params;

	const where = buildWhereConditions(search, filters);

	try {
		return await model.count({ where });
	} catch (error) {
		throw new Error(
			`Count query failed: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

/**
 * Get aggregated data (sum, avg, min, max, count)
 */
export async function aggregateRecords<F = Record<string, unknown>>(params: {
	model: PrismaModelDelegate;
	search?: SearchParams;
	filters?: FilterParams<F>;
	aggregations: {
		_count?: boolean | Record<string, boolean>;
		_sum?: Record<string, boolean>;
		_avg?: Record<string, boolean>;
		_min?: Record<string, boolean>;
		_max?: Record<string, boolean>;
	};
	groupBy?: string[];
}): Promise<unknown[]> {
	const { model, search, filters, aggregations, groupBy } = params;

	const where = buildWhereConditions(search, filters);

	try {
		if (groupBy && groupBy.length > 0) {
			// Use groupBy for aggregations
			return await (model as unknown as { groupBy: (args: unknown) => Promise<unknown[]> }).groupBy({
				by: groupBy,
				where,
				...aggregations,
			});
		}
		// Use aggregate for single aggregation
		return [
			await (model as unknown as { aggregate: (args: unknown) => Promise<unknown> }).aggregate({
				where,
				...aggregations,
			}),
		];
	} catch (error) {
		throw new Error(
			`Aggregation query failed: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

// ===== HELPER FUNCTIONS FOR COMMON PATTERNS =====

/**
 * Create date range filter helper
 */
export function createDateRangeFilter(field: string, from?: Date, to?: Date): FilterParams {
	return {
		dateRange: { field, from, to },
	};
}

/**
 * Create number range filter helper
 */
export function createNumberRangeFilter(field: string, min?: number, max?: number): FilterParams {
	return {
		numberRange: { field, min, max },
	};
}

/**
 * Create search parameters helper
 */
export function createSearchParams(
	fields: string[],
	term: string,
	mode: 'insensitive' | 'default' = 'insensitive'
): SearchParams {
	return { fields, term, mode };
}

/**
 * Create sort parameters helper
 */
export function createSortParams<T>(sorts: Array<{ field: keyof T; direction: 'asc' | 'desc' }>): SortParams<T> {
	return sorts.map(sort => ({ [sort.field]: sort.direction })) as SortParams<T>;
}

/**
 * Validate and sanitize search term
 */
export function sanitizeSearchTerm(term: string): string {
	return term.trim().replace(/[%_]/g, '\\$&'); // Escape SQL wildcards
}

/**
 * Build complex filter combinations
 */
export function combineFilters<T>(...filters: Array<FilterParams<T>>): FilterParams<T> {
	const result = {} as FilterParams<T>;
	for (const filter of filters) {
		for (const [key, value] of Object.entries(filter)) {
			(result as Record<string, unknown>)[key] = value;
		}
	}
	return result;
}
