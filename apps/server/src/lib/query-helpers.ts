/**
 * Reusable query parameter extraction helpers for Hono route handlers
 * Provides type-safe parameter parsing with validation and transformation
 */

import type { Context } from "hono";
import { sanitizeSearchTerm } from "./prisma-helpers";

// ===== TYPE DEFINITIONS =====

export interface PaginationParams {
	page?: number;
	limit?: number;
}

export interface SearchParams {
	search?: string;
}

export interface SortParams {
	sortBy?: string;
	sortOrder?: 'asc' | 'desc';
}

export interface DateFilterParams {
	createdAfter?: Date;
	createdBefore?: Date;
	updatedAfter?: Date;
	updatedBefore?: Date;
}

export interface QueryExtractionOptions {
	pagination?: {
		defaultPage?: number;
		defaultLimit?: number;
		maxLimit?: number;
	};
	search?: {
		sanitize?: boolean;
		minLength?: number;
	};
	sort?: {
		defaultSortBy?: string;
		defaultSortOrder?: 'asc' | 'desc';
		allowedSortFields?: string[];
	};
	dateFilters?: {
		fields?: Array<'createdAfter' | 'createdBefore' | 'updatedAfter' | 'updatedBefore'>;
	};
	customEnums?: Record<string, string[]>;
}

export type ExtractedQueryParams<T = Record<string, unknown>> = {
	pagination: PaginationParams;
	search: SearchParams;
	sort: SortParams;
	dateFilters: DateFilterParams;
	custom: T;
};

// ===== HELPER FUNCTIONS =====

/**
 * Safely convert string to number with fallback
 */
function parseNumber(value: string | undefined, fallback?: number): number | undefined {
	if (!value) return fallback;
	const parsed = Number(value);
	return isNaN(parsed) ? fallback : parsed;
}

/**
 * Safely parse date string to Date object
 */
function parseDate(value: string | undefined): Date | undefined {
	if (!value) return undefined;
	
	try {
		const date = new Date(value);
		// Check if date is valid
		if (isNaN(date.getTime())) {
			return undefined;
		}
		return date;
	} catch {
		return undefined;
	}
}

/**
 * Validate enum value against allowed options
 */
function validateEnum(value: string | undefined, allowedValues: string[]): string | undefined {
	if (!value) return undefined;
	return allowedValues.includes(value) ? value : undefined;
}

/**
 * Extract pagination parameters from request
 */
export function extractPaginationParams(
	c: Context,
	options: QueryExtractionOptions['pagination'] = {}
): PaginationParams {
	const {
		defaultPage = 1,
		defaultLimit = 10,
		maxLimit = 100,
	} = options;

	const page = parseNumber(c.req.query("page"), defaultPage);
	const rawLimit = parseNumber(c.req.query("limit"), defaultLimit);
	const limit = rawLimit ? Math.min(rawLimit, maxLimit) : defaultLimit;

	return {
		page: page && page > 0 ? page : defaultPage,
		limit: limit > 0 ? limit : defaultLimit,
	};
}

/**
 * Extract search parameters from request
 */
export function extractSearchParams(
	c: Context,
	options: QueryExtractionOptions['search'] = {}
): SearchParams {
	const {
		sanitize = true,
		minLength = 1,
	} = options;

	const rawSearch = c.req.query("search");
	
	if (!rawSearch || rawSearch.trim().length < minLength) {
		return { search: undefined };
	}

	const search = sanitize ? sanitizeSearchTerm(rawSearch.trim()) : rawSearch.trim();
	
	return { search };
}

/**
 * Extract sort parameters from request
 */
export function extractSortParams(
	c: Context,
	options: QueryExtractionOptions['sort'] = {}
): SortParams {
	const {
		defaultSortBy,
		defaultSortOrder = 'desc',
		allowedSortFields,
	} = options;

	const rawSortBy = c.req.query("sortBy");
	const rawSortOrder = c.req.query("sortOrder") as 'asc' | 'desc' | undefined;

	// Validate sortBy against allowed fields if specified
	let sortBy = rawSortBy;
	if (allowedSortFields && rawSortBy) {
		sortBy = allowedSortFields.includes(rawSortBy) ? rawSortBy : defaultSortBy;
	} else if (!rawSortBy) {
		sortBy = defaultSortBy;
	}

	// Validate sortOrder
	const sortOrder = rawSortOrder === 'asc' || rawSortOrder === 'desc' 
		? rawSortOrder 
		: defaultSortOrder;

	return {
		sortBy,
		sortOrder,
	};
}

/**
 * Extract date filter parameters from request
 */
export function extractDateFilterParams(
	c: Context,
	options: QueryExtractionOptions['dateFilters'] = {}
): DateFilterParams {
	const {
		fields = ['createdAfter', 'createdBefore', 'updatedAfter', 'updatedBefore'],
	} = options;

	const dateFilters: DateFilterParams = {};

	for (const field of fields) {
		const value = c.req.query(field);
		if (value) {
			dateFilters[field] = parseDate(value);
		}
	}

	return dateFilters;
}

/**
 * Extract custom enum parameters from request
 */
export function extractCustomParams<T = Record<string, unknown>>(
	c: Context,
	enumDefinitions: Record<string, string[]> = {}
): T {
	const custom: Record<string, unknown> = {};

	for (const [paramName, allowedValues] of Object.entries(enumDefinitions)) {
		const value = c.req.query(paramName);
		custom[paramName] = validateEnum(value, allowedValues);
	}

	return custom as T;
}

// ===== MAIN EXTRACTION FUNCTION =====

/**
 * Extract all query parameters from request with comprehensive validation
 * 
 * @param c - Hono context object
 * @param options - Configuration options for parameter extraction
 * @returns Extracted and validated query parameters
 */
export function extractQueryParams<T = Record<string, unknown>>(
	c: Context,
	options: QueryExtractionOptions = {}
): ExtractedQueryParams<T> {
	const pagination = extractPaginationParams(c, options.pagination);
	const search = extractSearchParams(c, options.search);
	const sort = extractSortParams(c, options.sort);
	const dateFilters = extractDateFilterParams(c, options.dateFilters);
	const custom = extractCustomParams<T>(c, options.customEnums);

	return {
		pagination,
		search,
		sort,
		dateFilters,
		custom,
	};
}

// ===== CONVENIENCE FUNCTIONS FOR COMMON USE CASES =====

/**
 * Extract organization-specific query parameters
 */
export function extractOrganizationQueryParams(c: Context) {
	return extractQueryParams<{ type?: 'PERSONAL' | 'TEAM' | 'ENTERPRISE' }>(c, {
		pagination: {
			defaultPage: 1,
			defaultLimit: 10,
			maxLimit: 50,
		},
		search: {
			sanitize: true,
			minLength: 1,
		},
		sort: {
			defaultSortBy: 'createdAt',
			defaultSortOrder: 'desc',
			allowedSortFields: ['name', 'createdAt', 'updatedAt', 'type'],
		},
		dateFilters: {
			fields: ['createdAfter', 'createdBefore'],
		},
		customEnums: {
			type: ['PERSONAL', 'TEAM', 'ENTERPRISE'],
		},
	});
}

/**
 * Extract user-specific query parameters
 */
export function extractUserQueryParams(c: Context) {
	return extractQueryParams<{ status?: 'ACTIVE' | 'INACTIVE' | 'PENDING' }>(c, {
		pagination: {
			defaultPage: 1,
			defaultLimit: 20,
			maxLimit: 100,
		},
		search: {
			sanitize: true,
			minLength: 2,
		},
		sort: {
			defaultSortBy: 'createdAt',
			defaultSortOrder: 'desc',
			allowedSortFields: ['name', 'email', 'createdAt', 'updatedAt'],
		},
		dateFilters: {
			fields: ['createdAfter', 'updatedAfter'],
		},
		customEnums: {
			status: ['ACTIVE', 'INACTIVE', 'PENDING'],
		},
	});
}

/**
 * Extract invitation-specific query parameters
 */
export function extractInvitationQueryParams(c: Context) {
	return extractQueryParams<{ status?: 'PENDING' | 'ACCEPTED' | 'EXPIRED' | 'REVOKED' }>(c, {
		pagination: {
			defaultPage: 1,
			defaultLimit: 25,
			maxLimit: 100,
		},
		search: {
			sanitize: true,
			minLength: 2,
		},
		sort: {
			defaultSortBy: 'createdAt',
			defaultSortOrder: 'desc',
			allowedSortFields: ['email', 'createdAt', 'expiresAt', 'status'],
		},
		dateFilters: {
			fields: ['createdAfter', 'createdBefore'],
		},
		customEnums: {
			status: ['PENDING', 'ACCEPTED', 'EXPIRED', 'REVOKED'],
		},
	});
}
