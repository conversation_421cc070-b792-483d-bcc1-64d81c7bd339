import { expo } from "@better-auth/expo";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import prisma from "../../prisma";
import {
	createOrganizationWithAdmin,
	getUserOrganizations,
} from "./organization";
import {
	generateSmartOrganizationData,
	generateUniqueSlug,
} from "./organization-naming";

export const auth = betterAuth({
	database: prismaAdapter(prisma, {
		provider: "postgresql",
	}),
	trustedOrigins: [process.env.CORS_ORIGIN || "", "my-better-t-app://"],
	emailAndPassword: {
		enabled: true,
	},
	advanced: {
		defaultCookieAttributes: {
			sameSite: "none",
			secure: true,
			httpOnly: true,
		},
	},
	plugins: [expo()],
	session: {
		// Extend session with organization context
		updateAge: 3600, // 1 hour in seconds
	},
	databaseHooks: {
		user: {
			create: {
				after: async (user) => {
					// Auto-create organization for new users with smart naming
					if (user?.name && user?.email) {
						const userId = user.id;

						// Check if user already has organizations
						const existingOrgs = await getUserOrganizations(userId);

						if (existingOrgs.length === 0) {
							// Generate smart organization data based on user info
							const orgData = generateSmartOrganizationData({
								name: user.name,
								email: user.email,
							});

							// Ensure slug uniqueness
							const uniqueSlug = await generateUniqueSlug(orgData.slug);

							// Create organization with smart defaults
							await createOrganizationWithAdmin(userId, {
								name: orgData.name,
								slug: uniqueSlug,
								description: orgData.description,
								type: orgData.type,
							});
						}
					}
				},
			},
		},
	},
});
