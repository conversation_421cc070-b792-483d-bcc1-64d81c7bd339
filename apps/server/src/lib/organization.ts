import {
	checkPermissionFromMembership,
	type OrganizationMembershipData,
	PERMISSIONS,
	type Permission,
} from "@saas-template/schemas";
import type { Role } from "prisma/generated/client";
import prisma from "../../prisma";

// ===== RBAC FUNCTIONS =====

// Helper function to check if a user has a specific permission in an organization
export async function hasPermission(
	userId: string,
	organizationId: string,
	permission: Permission,
): Promise<boolean> {
	const membership = await prisma.organizationMember.findUnique({
		where: {
			userId_organizationId: {
				userId,
				organizationId,
			},
		},
		select: {
			userId: true,
			organizationId: true,
			role: true,
		},
	});

	// Use shared permission checking logic
	return checkPermissionFromMembership(
		membership as OrganizationMembershipData | null,
		permission,
	);
}

// Helper function to get user's role in an organization
export async function getUserRole(
	userId: string,
	organizationId: string,
): Promise<Role | null> {
	const membership = await prisma.organizationMember.findUnique({
		where: {
			userId_organizationId: {
				userId,
				organizationId,
			},
		},
	});

	return membership?.role || null;
}

// Helper function to get user's primary organization (first one they joined)
export async function getPrimaryOrganization(userId: string) {
	const membership = await prisma.organizationMember.findFirst({
		where: { userId },
		include: {
			organization: true,
		},
		orderBy: {
			joinedAt: "asc",
		},
	});

	return membership?.organization || null;
}

// Middleware function to check organization access
export function requireOrganizationAccess(permission?: string) {
	return async (userId: string, organizationId: string) => {
		if (!userId || !organizationId) {
			throw new Error("User ID and Organization ID are required");
		}

		const membership = await prisma.organizationMember.findUnique({
			where: {
				userId_organizationId: {
					userId,
					organizationId,
				},
			},
		});

		if (!membership) {
			throw new Error(
				"Access denied: User is not a member of this organization",
			);
		}

		if (
			permission &&
			!(PERMISSIONS[membership.role] as readonly string[]).includes(permission)
		) {
			throw new Error(
				`Access denied: Insufficient permissions for ${permission}`,
			);
		}

		return membership;
	};
}

// Helper function to create organization with admin user
export async function createOrganizationWithAdmin(
	userId: string,
	organizationData: {
		name: string;
		slug: string;
		description?: string;
		type?: "PERSONAL" | "TEAM" | "ENTERPRISE";
	},
) {
	return await prisma.$transaction(async (tx) => {
		// Create organization
		const organization = await tx.organization.create({
			data: {
				...organizationData,
				type: organizationData.type || "PERSONAL",
				lastActivityAt: new Date(), // Set initial activity
			},
		});

		// Add user as admin
		await tx.organizationMember.create({
			data: {
				userId,
				organizationId: organization.id,
				role: "ADMIN",
			},
		});

		return organization;
	});
}

// ===== ORGANIZATION CRUD OPERATIONS =====

// Get organizations for a user with advanced querying capabilities
export async function getUserOrganizations(
	userId: string,
	options: {
		page?: number;
		limit?: number;
		search?: string;
		type?: "PERSONAL" | "TEAM" | "ENTERPRISE";
		sortBy?: string;
		sortOrder?: "asc" | "desc";
		createdAfter?: Date;
		includeStats?: boolean;
	} = {},
) {
	const {
		page = 1,
		limit = 10,
		search,
		type,
		sortBy = "createdAt",
		sortOrder = "desc",
		createdAfter,
		includeStats = false,
	} = options;

	// Build where conditions
	const whereConditions: Record<string, unknown> = {
		members: {
			some: {
				userId,
			},
		},
	};

	// Add type filter
	if (type) {
		whereConditions.type = type;
	}

	// Add date filter
	if (createdAfter) {
		whereConditions.createdAt = {
			gte: createdAfter,
		};
	}

	// Add search conditions
	if (search?.trim()) {
		const searchConditions = [
			{
				name: {
					contains: search.trim(),
					mode: "insensitive" as const,
				},
			},
			{
				description: {
					contains: search.trim(),
					mode: "insensitive" as const,
				},
			},
		];
		whereConditions.OR = searchConditions;
	}

	// Calculate offset for pagination
	const skip = (page - 1) * Math.min(limit, 50); // Max 50 per page
	const take = Math.min(limit, 50);

	// Execute queries in parallel
	const [organizations, totalCount] = await Promise.all([
		prisma.organization.findMany({
			where: whereConditions,
			skip,
			take,
			orderBy: { [sortBy]: sortOrder },
			include: {
				members: {
					where: { userId },
					select: { role: true, joinedAt: true },
				},
				...(includeStats && {
					_count: {
						select: { members: true },
					},
				}),
			},
		}),
		prisma.organization.count({ where: whereConditions }),
	]);

	// Calculate pagination metadata
	const totalPages = Math.ceil(totalCount / take);
	const hasNext = page < totalPages;
	const hasPrevious = page > 1;

	// Transform results to include user's role and member count
	const transformedData = organizations.map((org) => ({
		id: org.id,
		name: org.name,
		slug: org.slug,
		description: org.description,
		type: org.type,
		createdAt: org.createdAt,
		updatedAt: org.updatedAt,
		role: org.members[0]?.role || "USER",
		joinedAt: org.members[0]?.joinedAt,
		...(includeStats && {
			memberCount:
				(org as unknown as { _count?: { members: number } })._count?.members ||
				0,
		}),
	}));

	return {
		data: transformedData,
		meta: {
			currentPage: page,
			totalPages,
			totalCount,
			limit: take,
			hasNext,
			hasPrevious,
		},
	};
}

// Search organizations for a user with advanced filtering
export async function searchUserOrganizations(
	userId: string,
	searchTerm: string,
	options?: {
		limit?: number;
		type?: "PERSONAL" | "TEAM" | "ENTERPRISE";
		sortBy?: string;
		sortOrder?: "asc" | "desc";
	},
) {
	const {
		limit = 20,
		type,
		sortBy = "name",
		sortOrder = "asc",
	} = options || {};

	// Validate and sanitize search term
	const cleanSearchTerm = searchTerm.trim();
	if (!cleanSearchTerm) {
		throw new Error("Search term is required");
	}

	// Build where conditions
	const whereConditions: Record<string, unknown> = {
		members: {
			some: {
				userId,
			},
		},
		OR: [
			{
				name: {
					contains: cleanSearchTerm,
					mode: "insensitive" as const,
				},
			},
			{
				description: {
					contains: cleanSearchTerm,
					mode: "insensitive" as const,
				},
			},
		],
	};

	// Add type filter if provided
	if (type) {
		whereConditions.type = type;
	}

	try {
		// Execute search query
		const organizations = await prisma.organization.findMany({
			where: whereConditions,
			take: Math.min(limit, 50), // Max 50 results
			orderBy: { [sortBy]: sortOrder },
			select: {
				id: true,
				name: true,
				slug: true,
				description: true,
				type: true,
				createdAt: true,
			},
		});

		return {
			results: organizations,
			count: organizations.length,
			searchTerm: cleanSearchTerm,
		};
	} catch (error) {
		throw new Error(
			`Search failed: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

// Get organization statistics for a user
export async function getUserOrganizationStats(
	userId: string,
	options?: {
		days?: number;
	},
) {
	const { days = 30 } = options || {};

	// Calculate date threshold for "recent" organizations
	const fromDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

	// Base filter for user's organizations
	const baseFilter = {
		members: {
			some: { userId },
		},
	};

	// Recent filter with date range
	const recentFilter = {
		members: {
			some: { userId },
		},
		createdAt: {
			gte: fromDate,
		},
	};

	try {
		// Execute multiple queries in parallel for better performance
		const [totalCount, recentCount, typeStats] = await Promise.all([
			// Total organizations count
			prisma.organization.count({
				where: baseFilter,
			}),

			// Recent organizations count
			prisma.organization.count({
				where: recentFilter,
			}),

			// Organizations by type
			prisma.organization.groupBy({
				by: ["type"],
				where: baseFilter,
				_count: { id: true },
			}),
		]);

		// Transform type stats into a more usable format
		const typeBreakdown = typeStats.reduce(
			(acc, stat) => {
				acc[stat.type] = stat._count.id;
				return acc;
			},
			{} as Record<string, number>,
		);

		return {
			total: totalCount,
			recent: recentCount,
			recentDays: days,
			byType: typeBreakdown,
		};
	} catch (error) {
		throw new Error(
			`Statistics query failed: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

export interface CreateOrganizationData {
	name: string;
	slug: string;
	description?: string;
}

export interface UpdateOrganizationData {
	name?: string;
	slug?: string;
	description?: string;
}

export interface AddMemberData {
	userId: string;
	role: Role;
}

// Organization CRUD operations
// Create organization (only for admins or new users)
export async function createOrganization(
	creatorId: string,
	data: CreateOrganizationData,
) {
	return await prisma.$transaction(async (tx) => {
		// Check if slug is unique
		const existingOrg = await tx.organization.findUnique({
			where: { slug: data.slug },
		});

		if (existingOrg) {
			throw new Error("Organization slug already exists");
		}

		// Create organization
		const organization = await tx.organization.create({
			data,
		});

		// Add creator as admin
		await tx.organizationMember.create({
			data: {
				userId: creatorId,
				organizationId: organization.id,
				role: "ADMIN",
			},
		});

		return organization;
	});
}

// Get organization by ID (with member check)
export async function getOrganizationById(
	organizationId: string,
	userId: string,
) {
	await requireOrganizationAccess("organization:read")(userId, organizationId);

	return await prisma.organization.findUnique({
		where: { id: organizationId },
		include: {
			members: {
				include: {
					user: {
						select: {
							id: true,
							name: true,
							email: true,
							image: true,
						},
					},
				},
			},
			_count: {
				select: {
					members: true,
					invitations: true,
				},
			},
		},
	});
}

// Update organization
export async function updateOrganization(
	organizationId: string,
	userId: string,
	data: UpdateOrganizationData,
) {
	await requireOrganizationAccess("organization:update")(
		userId,
		organizationId,
	);

	// Check slug uniqueness if updating slug
	if (data.slug) {
		const existingOrg = await prisma.organization.findFirst({
			where: {
				slug: data.slug,
				id: { not: organizationId },
			},
		});

		if (existingOrg) {
			throw new Error("Organization slug already exists");
		}
	}

	return await prisma.organization.update({
		where: { id: organizationId },
		data,
	});
}

// Delete organization (admin only)
export async function deleteOrganization(
	organizationId: string,
	userId: string,
) {
	await requireOrganizationAccess("organization:delete")(
		userId,
		organizationId,
	);

	return await prisma.organization.delete({
		where: { id: organizationId },
	});
}

// ===== MEMBERSHIP MANAGEMENT FUNCTIONS =====

// Add member to organization
export async function addMemberToOrganization(
	organizationId: string,
	adminId: string,
	memberData: AddMemberData,
) {
	await requireOrganizationAccess("organization:manage_members")(
		adminId,
		organizationId,
	);

	// Check if user is already a member
	const existingMember = await prisma.organizationMember.findUnique({
		where: {
			userId_organizationId: {
				userId: memberData.userId,
				organizationId,
			},
		},
	});

	if (existingMember) {
		throw new Error("User is already a member of this organization");
	}

	return await prisma.organizationMember.create({
		data: {
			userId: memberData.userId,
			organizationId,
			role: memberData.role,
		},
		include: {
			user: {
				select: {
					id: true,
					name: true,
					email: true,
					image: true,
				},
			},
		},
	});
}

// Update member role
export async function updateMemberRole(
	organizationId: string,
	adminId: string,
	memberId: string,
	newRole: Role,
) {
	await requireOrganizationAccess("user:manage_roles")(adminId, organizationId);

	return await prisma.organizationMember.update({
		where: {
			userId_organizationId: {
				userId: memberId,
				organizationId,
			},
		},
		data: { role: newRole },
		include: {
			user: {
				select: {
					id: true,
					name: true,
					email: true,
					image: true,
				},
			},
		},
	});
}

// Remove member from organization
export async function removeMemberFromOrganization(
	organizationId: string,
	adminId: string,
	memberId: string,
) {
	await requireOrganizationAccess("organization:manage_members")(
		adminId,
		organizationId,
	);

	// Prevent removing the last admin
	const adminCount = await prisma.organizationMember.count({
		where: {
			organizationId,
			role: "ADMIN",
		},
	});

	const memberToRemove = await prisma.organizationMember.findUnique({
		where: {
			userId_organizationId: {
				userId: memberId,
				organizationId,
			},
		},
	});

	if (memberToRemove?.role === "ADMIN" && adminCount <= 1) {
		throw new Error("Cannot remove the last admin from the organization");
	}

	return await prisma.organizationMember.delete({
		where: {
			userId_organizationId: {
				userId: memberId,
				organizationId,
			},
		},
	});
}

// Get organization members
export async function getOrganizationMembers(
	organizationId: string,
	userId: string,
) {
	await requireOrganizationAccess("organization:read")(userId, organizationId);

	return await prisma.organizationMember.findMany({
		where: { organizationId },
		include: {
			user: {
				select: {
					id: true,
					name: true,
					email: true,
					image: true,
				},
			},
		},
		orderBy: [
			{ role: "asc" }, // ADMIN first, then COLLABORATOR, then USER
			{ joinedAt: "asc" },
		],
	});
}
