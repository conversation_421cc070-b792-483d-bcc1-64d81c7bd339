import { randomBytes } from "node:crypto";
import type { InvitationStatus, Role } from "prisma/generated/client";
import prisma from "../../prisma";
import { hasPermission, requireOrganizationAccess } from "./organization";

export interface CreateInvitationData {
	email: string;
	role: Role;
	organizationId: string;
}

export interface InvitationFilters {
	status?: InvitationStatus;
	email?: string;
	role?: Role;
}

// Invitation management service
// Create invitation
export async function createInvitation(
	inviterId: string,
	data: CreateInvitationData,
) {
	const { email, role, organizationId } = data;

	// Check if inviter has permission to create invitations
	await requireOrganizationAccess("invitation:create")(
		inviterId,
		organizationId,
	);

	// Check if inviter can invite users with the specified role
	if (role === "ADMIN" || role === "COLLABORATOR") {
		const canInviteAnyRole = await hasPermission(
			inviterId,
			organizationId,
			"user:invite_any_role",
		);
		if (!canInviteAnyRole) {
			throw new Error(
				"Insufficient permissions to invite users with this role",
			);
		}
	}

	// Check if user is already a member
	const existingMember = await prisma.organizationMember.findFirst({
		where: {
			organizationId,
			user: { email },
		},
	});

	if (existingMember) {
		throw new Error("User is already a member of this organization");
	}

	// Check if there's already a pending invitation
	const existingInvitation = await prisma.invitation.findFirst({
		where: {
			email,
			organizationId,
			status: "PENDING",
		},
	});

	if (existingInvitation) {
		throw new Error("Invitation already sent to this email");
	}

	// Generate unique token
	const token = randomBytes(32).toString("hex");

	// Set expiration (7 days from now)
	const expiresAt = new Date();
	expiresAt.setDate(expiresAt.getDate() + 7);

	return await prisma.invitation.create({
		data: {
			email,
			organizationId,
			invitedById: inviterId,
			role,
			token,
			expiresAt,
		},
		include: {
			organization: {
				select: {
					id: true,
					name: true,
					slug: true,
				},
			},
			invitedBy: {
				select: {
					id: true,
					name: true,
					email: true,
				},
			},
		},
	});
}

// Get invitation by token
export async function getInvitationByToken(token: string) {
	const invitation = await prisma.invitation.findUnique({
		where: { token },
		include: {
			organization: {
				select: {
					id: true,
					name: true,
					slug: true,
					description: true,
				},
			},
			invitedBy: {
				select: {
					id: true,
					name: true,
					email: true,
				},
			},
		},
	});

	if (!invitation) {
		throw new Error("Invitation not found");
	}

	if (invitation.status !== "PENDING") {
		throw new Error("Invitation is no longer valid");
	}

	if (invitation.expiresAt < new Date()) {
		// Mark as expired
		await prisma.invitation.update({
			where: { id: invitation.id },
			data: { status: "EXPIRED" },
		});
		throw new Error("Invitation has expired");
	}

	return invitation;
}

// Accept invitation
export async function acceptInvitation(token: string, userId: string) {
	return await prisma.$transaction(async (tx) => {
		// Get and validate invitation
		const invitation = await tx.invitation.findUnique({
			where: { token },
			include: { organization: true },
		});

		if (!invitation) {
			throw new Error("Invitation not found");
		}

		if (invitation.status !== "PENDING") {
			throw new Error("Invitation is no longer valid");
		}

		if (invitation.expiresAt < new Date()) {
			await tx.invitation.update({
				where: { id: invitation.id },
				data: { status: "EXPIRED" },
			});
			throw new Error("Invitation has expired");
		}

		// Check if user email matches invitation email
		const user = await tx.user.findUnique({
			where: { id: userId },
		});

		if (!user || user.email !== invitation.email) {
			throw new Error("User email does not match invitation");
		}

		// Check if user is already a member
		const existingMember = await tx.organizationMember.findUnique({
			where: {
				userId_organizationId: {
					userId,
					organizationId: invitation.organizationId,
				},
			},
		});

		if (existingMember) {
			throw new Error("User is already a member of this organization");
		}

		// Add user to organization
		const member = await tx.organizationMember.create({
			data: {
				userId,
				organizationId: invitation.organizationId,
				role: invitation.role,
			},
			include: {
				organization: true,
				user: {
					select: {
						id: true,
						name: true,
						email: true,
					},
				},
			},
		});

		// Mark invitation as accepted
		await tx.invitation.update({
			where: { id: invitation.id },
			data: {
				status: "ACCEPTED",
				acceptedAt: new Date(),
			},
		});

		return member;
	});
}

// Revoke invitation
export async function revokeInvitation(
	invitationId: string,
	revokerId: string,
) {
	const invitation = await prisma.invitation.findUnique({
		where: { id: invitationId },
	});

	if (!invitation) {
		throw new Error("Invitation not found");
	}

	await requireOrganizationAccess("invitation:revoke")(
		revokerId,
		invitation.organizationId,
	);

	return await prisma.invitation.update({
		where: { id: invitationId },
		data: { status: "REVOKED" },
	});
}

// List invitations for organization
export async function listInvitations(
	organizationId: string,
	userId: string,
	filters: InvitationFilters = {},
) {
	await requireOrganizationAccess("invitation:read")(userId, organizationId);

	const where: Record<string, unknown> = { organizationId };

	if (filters.status) {
		where.status = filters.status;
	}
	if (filters.email) {
		where.email = { contains: filters.email, mode: "insensitive" };
	}
	if (filters.role) {
		where.role = filters.role;
	}

	return await prisma.invitation.findMany({
		where,
		include: {
			invitedBy: {
				select: {
					id: true,
					name: true,
					email: true,
				},
			},
		},
		orderBy: { createdAt: "desc" },
	});
}

// Clean up expired invitations (utility function)
export async function cleanupExpiredInvitations() {
	const now = new Date();

	return await prisma.invitation.updateMany({
		where: {
			status: "PENDING",
			expiresAt: { lt: now },
		},
		data: { status: "EXPIRED" },
	});
}
