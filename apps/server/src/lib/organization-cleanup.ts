import prisma from "../../prisma";
import { updateOrganizationActivity } from "./organization-naming";

/**
 * Configuration for cleanup operations
 */
const CLEANUP_CONFIG = {
	// Days of inactivity before considering an organization for cleanup
	INACTIVE_DAYS: 30,
	// Only cleanup PERSONAL type organizations
	CLEANUP_TYPES: ["PERSONAL"] as Array<"PERSONAL">,
	// Batch size for processing organizations
	BATCH_SIZE: 50,
	// Dry run mode for testing
	DRY_RUN: process.env.CLEANUP_DRY_RUN === "true",
};

/**
 * Criteria for determining if an organization is unused
 */
interface UnusedOrganizationCriteria {
	// No projects or content created
	hasNoContent: boolean;
	// No invitations sent
	hasNoInvitations: boolean;
	// Owner hasn't logged in recently
	ownerInactive: boolean;
	// Organization hasn't been accessed recently
	organizationInactive: boolean;
	// Only has one member (the owner)
	singleMember: boolean;
}

/**
 * Result of cleanup operation
 */
interface CleanupResult {
	processed: number;
	archived: number;
	errors: string[];
	dryRun: boolean;
}

/**
 * Main cleanup function for unused personal organizations
 */
export async function cleanupUnusedOrganizations(): Promise<CleanupResult> {
	const result: CleanupResult = {
		processed: 0,
		archived: 0,
		errors: [],
		dryRun: CLEANUP_CONFIG.DRY_RUN,
	};

	try {
		console.log(
			`Starting organization cleanup (DRY_RUN: ${CLEANUP_CONFIG.DRY_RUN})`,
		);

		// Get potentially unused organizations
		const candidates = await getUnusedOrganizationCandidates();
		console.log(
			`Found ${candidates.length} candidate organizations for cleanup`,
		);

		// Process in batches
		for (let i = 0; i < candidates.length; i += CLEANUP_CONFIG.BATCH_SIZE) {
			const batch = candidates.slice(i, i + CLEANUP_CONFIG.BATCH_SIZE);

			for (const org of batch) {
				try {
					result.processed++;

					// Evaluate if organization should be archived
					const criteria = await evaluateOrganization(org.id);

					if (shouldArchiveOrganization(criteria)) {
						if (!CLEANUP_CONFIG.DRY_RUN) {
							await archiveOrganization(org.id);
						}

						result.archived++;
						console.log(
							`${CLEANUP_CONFIG.DRY_RUN ? "[DRY RUN] " : ""}Archived organization: ${org.name} (${org.id})`,
						);
					}
				} catch (error) {
					const errorMessage =
						error instanceof Error ? error.message : "Unknown error";
					const errorMsg = `Failed to process organization ${org.id}: ${errorMessage}`;
					result.errors.push(errorMsg);
					console.error(errorMsg);
				}
			}
		}

		console.log(
			`Cleanup completed. Processed: ${result.processed}, Archived: ${result.archived}, Errors: ${result.errors.length}`,
		);
		return result;
	} catch (error) {
		const errorMessage =
			error instanceof Error ? error.message : "Unknown error";
		const errorMsg = `Cleanup operation failed: ${errorMessage}`;
		result.errors.push(errorMsg);
		console.error(errorMsg);
		return result;
	}
}

/**
 * Get organizations that are candidates for cleanup
 */
async function getUnusedOrganizationCandidates() {
	const cutoffDate = new Date();
	cutoffDate.setDate(cutoffDate.getDate() - CLEANUP_CONFIG.INACTIVE_DAYS);

	return await prisma.organization.findMany({
		where: {
			type: { in: CLEANUP_CONFIG.CLEANUP_TYPES },
			createdAt: { lt: cutoffDate },
			// Only consider organizations that haven't been active recently
			OR: [{ lastActivityAt: null }, { lastActivityAt: { lt: cutoffDate } }],
		},
		include: {
			members: {
				include: {
					user: {
						select: {
							id: true,
							name: true,
							email: true,
							// Note: Better Auth doesn't have lastLoginAt by default
							// You might need to track this separately
						},
					},
				},
			},
			invitations: {
				select: { id: true },
			},
		},
		take: 1000, // Limit to prevent memory issues
	});
}

/**
 * Evaluate if an organization meets cleanup criteria
 */
async function evaluateOrganization(
	organizationId: string,
): Promise<UnusedOrganizationCriteria> {
	const org = await prisma.organization.findUnique({
		where: { id: organizationId },
		include: {
			members: {
				include: {
					user: true,
				},
			},
			invitations: true,
		},
	});

	if (!org) {
		throw new Error(`Organization ${organizationId} not found`);
	}

	const cutoffDate = new Date();
	cutoffDate.setDate(cutoffDate.getDate() - CLEANUP_CONFIG.INACTIVE_DAYS);

	return {
		hasNoContent: await hasNoContent(organizationId),
		hasNoInvitations: org.invitations.length === 0,
		ownerInactive: await isOwnerInactive(org.members, cutoffDate),
		organizationInactive:
			!org.lastActivityAt || org.lastActivityAt < cutoffDate,
		singleMember: org.members.length === 1,
	};
}

/**
 * Check if organization has any content/projects
 * This is a placeholder - you'll need to implement based on your domain models
 */
async function hasNoContent(_organizationId: string): Promise<boolean> {
	// TODO: Add checks for your specific content types
	// Examples:
	// - Projects
	// - Documents
	// - Files
	// - Any other domain-specific content

	// For now, assume no content if no additional data exists
	// You can extend this based on your application's needs
	return true;
}

/**
 * Check if the organization owner has been inactive
 */
async function isOwnerInactive(
	members: Array<{
		role: string;
		user: { id: string; name: string; email: string };
	}>,
	_cutoffDate: Date,
): Promise<boolean> {
	const admin = members.find((m) => m.role === "ADMIN");
	if (!admin) return true;

	// Since Better Auth doesn't track lastLoginAt by default,
	// you might need to implement this tracking separately
	// For now, we'll use a conservative approach
	return false; // Don't archive based on login activity alone
}

/**
 * Determine if organization should be archived based on criteria
 */
function shouldArchiveOrganization(
	criteria: UnusedOrganizationCriteria,
): boolean {
	// Archive if ALL of these conditions are met:
	return (
		criteria.hasNoContent &&
		criteria.hasNoInvitations &&
		criteria.organizationInactive &&
		criteria.singleMember
		// Note: Not using ownerInactive for now since we don't track login times
	);
}

/**
 * Archive an organization (soft delete)
 */
async function archiveOrganization(organizationId: string): Promise<void> {
	await prisma.$transaction(async (tx) => {
		// You could add an 'archived' field to your schema instead of deleting
		// For now, we'll add a suffix to the slug to mark it as archived
		const org = await tx.organization.findUnique({
			where: { id: organizationId },
		});

		if (!org) {
			throw new Error(`Organization ${organizationId} not found`);
		}

		// Mark as archived by updating the slug and adding metadata
		await tx.organization.update({
			where: { id: organizationId },
			data: {
				slug: `${org.slug}-archived-${Date.now()}`,
				description: `[ARCHIVED] ${org.description || "Unused personal workspace"}`,
				lastActivityAt: new Date(), // Update to prevent re-processing
			},
		});

		// Log the archival
		console.log(`Archived organization: ${org.name} (${organizationId})`);
	});
}

/**
 * Update organization activity when user performs actions
 * Call this from your application logic when users interact with organizations
 */
export async function trackOrganizationActivity(
	organizationId: string,
): Promise<void> {
	await updateOrganizationActivity(organizationId);
}

/**
 * Restore an archived organization
 */
export async function restoreArchivedOrganization(
	organizationId: string,
): Promise<void> {
	const org = await prisma.organization.findUnique({
		where: { id: organizationId },
	});

	if (!org) {
		throw new Error(`Organization ${organizationId} not found`);
	}

	if (!org.slug.includes("-archived-")) {
		throw new Error(`Organization ${organizationId} is not archived`);
	}

	// Remove archived suffix from slug
	const originalSlug = org.slug.replace(/-archived-\d+$/, "");
	const description = org.description?.replace(/^\[ARCHIVED\] /, "") || null;

	await prisma.organization.update({
		where: { id: organizationId },
		data: {
			slug: originalSlug,
			description,
			lastActivityAt: new Date(),
		},
	});

	console.log(`Restored organization: ${org.name} (${organizationId})`);
}
