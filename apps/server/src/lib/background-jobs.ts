import { cleanupUnusedOrganizations } from "./organization-cleanup";

/**
 * Background job scheduler for maintenance tasks
 */
class BackgroundJobScheduler {
	private intervals: Map<string, NodeJS.Timeout> = new Map();
	private isRunning = false;

	/**
	 * Start all background jobs
	 */
	start(): void {
		if (this.isRunning) {
			console.log("Background jobs already running");
			return;
		}

		console.log("Starting background jobs...");
		this.isRunning = true;

		// Schedule organization cleanup to run daily at 2 AM
		this.scheduleDaily(
			"organization-cleanup",
			this.runOrganizationCleanup,
			2,
			0,
		);

		console.log("Background jobs started successfully");
	}

	/**
	 * Stop all background jobs
	 */
	stop(): void {
		if (!this.isRunning) {
			return;
		}

		console.log("Stopping background jobs...");

		for (const [jobName, interval] of this.intervals) {
			clearInterval(interval);
			console.log(`Stopped job: ${jobName}`);
		}

		this.intervals.clear();
		this.isRunning = false;
		console.log("Background jobs stopped");
	}

	/**
	 * Schedule a job to run daily at a specific time
	 */
	private scheduleDaily(
		jobName: string,
		jobFunction: () => Promise<void>,
		hour: number,
		minute = 0,
	): void {
		const runJob = async () => {
			const now = new Date();
			const scheduledTime = new Date();
			scheduledTime.setHours(hour, minute, 0, 0);

			// If scheduled time has passed today, schedule for tomorrow
			if (now > scheduledTime) {
				scheduledTime.setDate(scheduledTime.getDate() + 1);
			}

			const timeUntilRun = scheduledTime.getTime() - now.getTime();

			setTimeout(async () => {
				try {
					console.log(`Running scheduled job: ${jobName}`);
					await jobFunction();
					console.log(`Completed scheduled job: ${jobName}`);
				} catch (error) {
					console.error(`Error in scheduled job ${jobName}:`, error);
				}

				// Schedule next run (24 hours later)
				const nextInterval = setInterval(
					async () => {
						try {
							console.log(`Running scheduled job: ${jobName}`);
							await jobFunction();
							console.log(`Completed scheduled job: ${jobName}`);
						} catch (error) {
							console.error(`Error in scheduled job ${jobName}:`, error);
						}
					},
					24 * 60 * 60 * 1000,
				); // 24 hours

				this.intervals.set(jobName, nextInterval);
			}, timeUntilRun);
		};

		runJob();
	}

	/**
	 * Organization cleanup job
	 */
	private async runOrganizationCleanup(): Promise<void> {
		try {
			const result = await cleanupUnusedOrganizations();
			console.log("Organization cleanup completed:", result);
		} catch (error) {
			console.error("Organization cleanup failed:", error);
		}
	}

	/**
	 * Run a job immediately (for testing/manual execution)
	 */
	async runJobNow(jobName: string): Promise<any> {
		switch (jobName) {
			case "organization-cleanup":
				return await this.runOrganizationCleanup();
			default:
				throw new Error(`Unknown job: ${jobName}`);
		}
	}

	/**
	 * Get status of all jobs
	 */
	getStatus(): { isRunning: boolean; activeJobs: string[] } {
		return {
			isRunning: this.isRunning,
			activeJobs: Array.from(this.intervals.keys()),
		};
	}
}

// Export singleton instance
export const backgroundJobs = new BackgroundJobScheduler();

/**
 * Initialize background jobs when server starts
 */
export function initializeBackgroundJobs(): void {
	// Only run background jobs in production or when explicitly enabled
	const shouldRunJobs =
		process.env.NODE_ENV === "production" ||
		process.env.ENABLE_BACKGROUND_JOBS === "true";

	if (shouldRunJobs) {
		backgroundJobs.start();

		// Graceful shutdown
		process.on("SIGTERM", () => {
			console.log("Received SIGTERM, stopping background jobs...");
			backgroundJobs.stop();
		});

		process.on("SIGINT", () => {
			console.log("Received SIGINT, stopping background jobs...");
			backgroundJobs.stop();
		});
	} else {
		console.log(
			"Background jobs disabled (set ENABLE_BACKGROUND_JOBS=true to enable)",
		);
	}
}
