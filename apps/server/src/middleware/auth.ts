import type { OrganizationMember } from "@saas-template/schemas";
import type { Context, Next } from "hono";
import { auth } from "../lib/auth";
import { requireOrganizationAccess } from "../lib/organization";

// Define proper types for auth context
export interface AuthUser {
	id: string;
	name: string;
	email: string;
	emailVerified: boolean;
	image?: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface AuthSession {
	id: string;
	userId: string;
	expiresAt: Date;
	token: string;
}

export interface AuthVariables {
	user: AuthUser;
	session: AuthSession;
	organizationId: string;
	membership: OrganizationMember;
	validatedBody: unknown;
}

// Middleware to require authentication
export async function requireAuth(c: Context, next: Next) {
	try {
		const session = await auth.api.getSession({
			headers: new Headers(c.req.header()),
		});

		if (!session?.user) {
			return c.json({ error: "Unauthorized" }, 401);
		}

		// Add user to context
		c.set("user", session.user);
		c.set("session", session);

		await next();
	} catch (error) {
		console.error("Auth middleware error:", error);
		return c.json({ error: "Authentication failed" }, 401);
	}
}

// Middleware to require organization membership
export function requireOrganizationMembership(permission?: string) {
	return async (c: Context, next: Next) => {
		const user = c.get("user");
		const organizationId =
			c.req.param("organizationId") || c.req.query("organizationId");

		if (!user) {
			return c.json({ error: "Unauthorized" }, 401);
		}

		if (!organizationId) {
			return c.json({ error: "Organization ID is required" }, 400);
		}

		try {
			const membership = await requireOrganizationAccess(permission)(
				user.id,
				organizationId,
			);

			// Add organization context to request
			c.set("organizationId", organizationId);
			c.set("membership", membership);

			await next();
		} catch (error) {
			console.error("Organization access error:", error);
			return c.json(
				{ error: error instanceof Error ? error.message : "Access denied" },
				403,
			);
		}
	};
}

// Middleware to validate request body
export function validateBody<T>(schema: (data: unknown) => T) {
	return async (c: Context, next: Next) => {
		try {
			const body = await c.req.json();
			const validatedData = schema(body);
			c.set("validatedBody", validatedData);
			await next();
		} catch (error) {
			console.error("Body validation error:", error);
			return c.json(
				{
					error: "Invalid request body",
					details: error instanceof Error ? error.message : "Validation failed",
				},
				400,
			);
		}
	};
}

// Helper function to get current user from context
export function getCurrentUser(c: Context) {
	const user = c.get("user");
	if (!user) {
		throw new Error("User not found in context");
	}
	return user;
}

// Helper function to get organization ID from context
export function getOrganizationId(c: Context) {
	const organizationId = c.get("organizationId");
	if (!organizationId) {
		throw new Error("Organization ID not found in context");
	}
	return organizationId;
}
