/**
 * Shared Hono context types for the application
 */

export type AppContext = {
	Variables: {
		user: {
			id: string;
			name: string;
			email: string;
			emailVerified: boolean;
			image?: string;
			createdAt: Date;
			updatedAt: Date;
		};
		session: {
			id: string;
			userId: string;
			expiresAt: Date;
			token: string;
		};
		organizationId: string;
		membership: {
			id: string;
			userId: string;
			organizationId: string;
			role: string;
			joinedAt: Date;
			updatedAt: Date;
		};
		validatedBody: unknown;
	};
};
