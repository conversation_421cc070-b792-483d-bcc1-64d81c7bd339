import { Hono } from "hono";
import { z } from "zod";
import {
	getPrimaryOrganization,
	getUserOrganizations,
} from "../lib/organization";
import { getCurrentUser, requireAuth, validateBody } from "../middleware/auth";
import type { AppContext } from "../types/hono";

const app = new Hono<AppContext>();

// Validation schemas
const switchOrganizationSchema = z.object({
	organizationId: z.string().min(1, "Organization ID is required"),
});

// Get current session with organization context
app.get("/", requireAuth, async (c) => {
	try {
		const user = getCurrentUser(c);

		// Get user's organizations
		const organizations = await getUserOrganizations(user.id);
		const primaryOrg = await getPrimaryOrganization(user.id);

		return c.json({
			user,
			session: {
				organizations: organizations.map((m) => ({
					id: m.organization.id,
					name: m.organization.name,
					slug: m.organization.slug,
					role: m.role,
					joinedAt: m.joinedAt,
				})),
				currentOrganization: primaryOrg
					? {
							id: primaryOrg.id,
							name: primaryOrg.name,
							slug: primaryOrg.slug,
						}
					: null,
			},
		});
	} catch (error) {
		console.error("Get session error:", error);
		return c.json({ error: "Failed to fetch session" }, 500);
	}
});

// Switch current organization context
app.post(
	"/switch-organization",
	requireAuth,
	validateBody(switchOrganizationSchema.parse),
	async (c) => {
		try {
			const user = getCurrentUser(c);
			const { organizationId } = c.get("validatedBody") as {
				organizationId: string;
			};

			// Verify user is a member of the organization
			const organizations = await getUserOrganizations(user.id);
			const targetOrg = organizations.find(
				(m) => m.organization.id === organizationId,
			);

			if (!targetOrg) {
				return c.json(
					{ error: "You are not a member of this organization" },
					403,
				);
			}

			// In a real implementation, you might want to store the current organization
			// in the session or a separate table. For now, we'll just return the organization info.
			return c.json({
				message: "Organization switched successfully",
				currentOrganization: {
					id: targetOrg.organization.id,
					name: targetOrg.organization.name,
					slug: targetOrg.organization.slug,
					role: targetOrg.role,
				},
			});
		} catch (error) {
			console.error("Switch organization error:", error);
			return c.json({ error: "Failed to switch organization" }, 500);
		}
	},
);

export default app;
