import {
	type AddMember,
	addMemberSchema,
	type CreateOrganization,
	createOrganizationSchema,
	type UpdateOrganization,
	updateMemberRoleSchema,
	updateOrganizationSchema,
} from "@saas-template/schemas";
import { Hono } from "hono";
import {
	addMemberToOrganization,
	createOrganization,
	deleteOrganization,
	getOrganizationById,
	getOrganizationMembers,
	getUserOrganizationStats,
	getUserOrganizations,
	removeMemberFromOrganization,
	searchUserOrganizations,
	updateMemberRole,
	updateOrganization,
} from "../lib/organization";
import { extractOrganizationQueryParams } from "../lib/query-helpers";
import {
	getCurrentUser,
	getOrganizationId,
	requireAuth,
	requireOrganizationMembership,
	validateBody,
} from "../middleware/auth";
import type { AppContext } from "../types/hono";

const app = new Hono<AppContext>();

// Schemas are now imported from @saas-template/schemas

// Get all organizations for current user with advanced pagination, search, and filtering
app.get("/", requireAuth, async (c) => {
	try {
		const user = getCurrentUser(c);

		// Extract query parameters using helper
		const queryParams = extractOrganizationQueryParams(c);

		// Use enhanced getUserOrganizations with extracted parameters
		const result = await getUserOrganizations(user.id, {
			page: queryParams.pagination.page,
			limit: queryParams.pagination.limit,
			search: queryParams.search.search,
			type: queryParams.custom.type,
			sortBy: queryParams.sort.sortBy,
			sortOrder: queryParams.sort.sortOrder,
			createdAfter: queryParams.dateFilters.createdAfter,
			includeStats: true,
		});

		return c.json(result);
	} catch (error) {
		console.error("Get organizations error:", error);
		return c.json({ error: "Failed to fetch organizations" }, 500);
	}
});

// Search organizations across all accessible organizations
app.get("/search", requireAuth, async (c) => {
	try {
		const user = getCurrentUser(c);
		const searchTerm = c.req.query("q");

		if (!searchTerm?.trim()) {
			return c.json({ error: "Search term is required" }, 400);
		}

		// Extract query parameters using helper (excluding the main search term)
		const queryParams = extractOrganizationQueryParams(c);

		// Use service layer function for search
		const result = await searchUserOrganizations(user.id, searchTerm.trim(), {
			limit: queryParams.pagination.limit,
			type: queryParams.custom.type,
			sortBy: queryParams.sort.sortBy,
			sortOrder: queryParams.sort.sortOrder,
		});

		return c.json(result);
	} catch (error) {
		console.error("Search organizations error:", error);

		// Handle business logic errors vs system errors
		if (error instanceof Error && error.message.includes('Search term is required')) {
			return c.json({ error: error.message }, 400);
		}

		return c.json({ error: "Failed to search organizations" }, 500);
	}
});

// Get organization statistics
app.get("/stats", requireAuth, async (c) => {
	try {
		const user = getCurrentUser(c);

		// Extract days parameter with validation
		const daysParam = c.req.query("days");
		const days = daysParam ? Math.max(1, Math.min(365, Number(daysParam) || 30)) : undefined;

		// Use service layer function for statistics
		const stats = await getUserOrganizationStats(user.id, { days });

		return c.json(stats);
	} catch (error) {
		console.error("Get organization stats error:", error);
		return c.json({ error: "Failed to fetch organization statistics" }, 500);
	}
});

// Create new organization
app.post(
	"/",
	requireAuth,
	validateBody(createOrganizationSchema.parse),
	async (c) => {
		try {
			const user = getCurrentUser(c);
			const data = c.get("validatedBody") as CreateOrganization;

			const organization = await createOrganization(user.id, data);

			return c.json({ organization }, 201);
		} catch (error) {
			console.error("Create organization error:", error);
			return c.json(
				{
					error:
						error instanceof Error
							? error.message
							: "Failed to create organization",
				},
				400,
			);
		}
	},
);

// Get organization by ID
app.get(
	"/:organizationId",
	requireAuth,
	requireOrganizationMembership("organization:read"),
	async (c) => {
		try {
			const user = getCurrentUser(c);
			const organizationId = getOrganizationId(c);

			const organization = await getOrganizationById(organizationId, user.id);

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			return c.json({ organization });
		} catch (error) {
			console.error("Get organization error:", error);
			return c.json({ error: "Failed to fetch organization" }, 500);
		}
	},
);

// Update organization
app.put(
	"/:organizationId",
	requireAuth,
	requireOrganizationMembership("organization:update"),
	validateBody(updateOrganizationSchema.parse),
	async (c) => {
		try {
			const user = getCurrentUser(c);
			const organizationId = getOrganizationId(c);
			const data = c.get("validatedBody") as UpdateOrganization;

			const organization = await updateOrganization(
				organizationId,
				user.id,
				data,
			);

			return c.json({ organization });
		} catch (error) {
			console.error("Update organization error:", error);
			return c.json(
				{
					error:
						error instanceof Error
							? error.message
							: "Failed to update organization",
				},
				400,
			);
		}
	},
);

// Delete organization
app.delete(
	"/:organizationId",
	requireAuth,
	requireOrganizationMembership("organization:delete"),
	async (c) => {
		try {
			const user = getCurrentUser(c);
			const organizationId = getOrganizationId(c);

			await deleteOrganization(organizationId, user.id);

			return c.json({ message: "Organization deleted successfully" });
		} catch (error) {
			console.error("Delete organization error:", error);
			return c.json(
				{
					error:
						error instanceof Error
							? error.message
							: "Failed to delete organization",
				},
				400,
			);
		}
	},
);

// Get organization members
app.get(
	"/:organizationId/members",
	requireAuth,
	requireOrganizationMembership("organization:read"),
	async (c) => {
		try {
			const user = getCurrentUser(c);
			const organizationId = getOrganizationId(c);

			const members = await getOrganizationMembers(organizationId, user.id);

			return c.json({ members });
		} catch (error) {
			console.error("Get members error:", error);
			return c.json({ error: "Failed to fetch members" }, 500);
		}
	},
);

// Add member to organization
app.post(
	"/:organizationId/members",
	requireAuth,
	requireOrganizationMembership("organization:manage_members"),
	validateBody(addMemberSchema.parse),
	async (c) => {
		try {
			const user = getCurrentUser(c);
			const organizationId = getOrganizationId(c);
			const data = c.get("validatedBody") as AddMember;

			const member = await addMemberToOrganization(
				organizationId,
				user.id,
				data,
			);

			return c.json({ member }, 201);
		} catch (error) {
			console.error("Add member error:", error);
			return c.json(
				{
					error:
						error instanceof Error ? error.message : "Failed to add member",
				},
				400,
			);
		}
	},
);

// Update member role
app.put(
	"/:organizationId/members/:memberId",
	requireAuth,
	requireOrganizationMembership("user:manage_roles"),
	validateBody(updateMemberRoleSchema.parse),
	async (c) => {
		try {
			const user = getCurrentUser(c);
			const organizationId = getOrganizationId(c);
			const memberId = c.req.param("memberId");
			const { role } = c.get("validatedBody") as {
				role: "ADMIN" | "COLLABORATOR" | "USER";
			};

			if (!memberId) {
				return c.json({ error: "Member ID is required" }, 400);
			}

			const member = await updateMemberRole(
				organizationId,
				user.id,
				memberId,
				role,
			);

			return c.json({ member });
		} catch (error) {
			console.error("Update member role error:", error);
			return c.json(
				{
					error:
						error instanceof Error
							? error.message
							: "Failed to update member role",
				},
				400,
			);
		}
	},
);

// Remove member from organization
app.delete(
	"/:organizationId/members/:memberId",
	requireAuth,
	requireOrganizationMembership("organization:manage_members"),
	async (c) => {
		try {
			const user = getCurrentUser(c);
			const organizationId = getOrganizationId(c);
			const memberId = c.req.param("memberId");

			if (!memberId) {
				return c.json({ error: "Member ID is required" }, 400);
			}

			await removeMemberFromOrganization(organizationId, user.id, memberId);

			return c.json({ message: "Member removed successfully" });
		} catch (error) {
			console.error("Remove member error:", error);
			return c.json(
				{
					error:
						error instanceof Error ? error.message : "Failed to remove member",
				},
				400,
			);
		}
	},
);

export default app;
