import type { OrganizationType } from "@saas-template/schemas";
import type { Context, Next } from "hono";
import { Hono } from "hono";
import prisma from "../../prisma";
import { backgroundJobs } from "../lib/background-jobs";
import { cleanupUnusedOrganizations } from "../lib/organization-cleanup";
import { getCurrentUser, requireAuth } from "../middleware/auth";
import type { AppContext } from "../types/hono";

const app = new Hono<AppContext>();

// Middleware to check if user is admin (you can customize this logic)
const requireAdmin = async (c: Context<AppContext>, next: Next) => {
	const user = getCurrentUser(c);

	// For now, check if user email matches admin email from env
	// You can implement more sophisticated admin checking
	const adminEmails = (process.env.ADMIN_EMAILS || "")
		.split(",")
		.map((e: string) => e.trim());

	if (!adminEmails.includes(user.email)) {
		return c.json({ error: "Admin access required" }, 403);
	}

	await next();
};

// Get background jobs status
app.get("/jobs/status", requireAuth, requireAdmin, async (c) => {
	try {
		const status = backgroundJobs.getStatus();
		return c.json(status);
	} catch (error) {
		console.error("Get jobs status error:", error);
		return c.json({ error: "Failed to get jobs status" }, 500);
	}
});

// Manually trigger organization cleanup
app.post(
	"/jobs/cleanup-organizations",
	requireAuth,
	requireAdmin,
	async (c) => {
		try {
			console.log(
				"Manual organization cleanup triggered by admin:",
				getCurrentUser(c).email,
			);

			const result = await cleanupUnusedOrganizations();

			return c.json({
				message: "Organization cleanup completed",
				result,
			});
		} catch (error) {
			console.error("Manual organization cleanup error:", error);
			return c.json({ error: "Failed to run organization cleanup" }, 500);
		}
	},
);

// Run any background job manually
app.post("/jobs/run/:jobName", requireAuth, requireAdmin, async (c) => {
	try {
		const jobName = c.req.param("jobName");
		const user = getCurrentUser(c);

		console.log(
			`Manual job execution triggered: ${jobName} by admin:`,
			user.email,
		);

		const result = await backgroundJobs.runJobNow(jobName);

		return c.json({
			message: `Job ${jobName} completed`,
			result,
		});
	} catch (error) {
		console.error("Manual job execution error:", error);
		const errorMessage =
			error instanceof Error ? error.message : "Unknown error";
		return c.json({ error: `Failed to run job: ${errorMessage}` }, 500);
	}
});

// Get organization statistics for monitoring
app.get("/stats/organizations", requireAuth, requireAdmin, async (c) => {
	try {
		const stats = await prisma.$transaction(async (tx) => {
			const total = await tx.organization.count();

			const byType = await tx.organization.groupBy({
				by: ["type"],
				_count: { id: true },
			});

			const cutoffDate = new Date();
			cutoffDate.setDate(cutoffDate.getDate() - 30);

			const inactive = await tx.organization.count({
				where: {
					OR: [
						{ lastActivityAt: null },
						{ lastActivityAt: { lt: cutoffDate } },
					],
				},
			});

			const archived = await tx.organization.count({
				where: {
					slug: { contains: "-archived-" },
				},
			});

			return {
				total,
				byType: byType.reduce(
					(acc: Record<OrganizationType, number>, item) => {
						acc[item.type] = item._count.id;
						return acc;
					},
					{} as Record<OrganizationType, number>,
				),
				inactive,
				archived,
			};
		});

		return c.json({
			organizations: stats,
			lastUpdated: new Date().toISOString(),
		});
	} catch (error) {
		console.error("Get organization stats error:", error);
		return c.json({ error: "Failed to get organization statistics" }, 500);
	}
});

export default app;
