import {
	type CustomizeOrganization,
	customizeOrganizationSchema,
} from "@saas-template/schemas";
import { Hono } from "hono";
import { getUserOrganizations, updateOrganization } from "../lib/organization";
import {
	generateNameSuggestions,
	generateUniqueSlug,
} from "../lib/organization-naming";
import { getCurrentUser, requireAuth, validateBody } from "../middleware/auth";
import type { AppContext } from "../types/hono";

const app = new Hono<AppContext>();

// Get onboarding data for the user's default organization
app.get("/organization", requireAuth, async (c) => {
	try {
		const user = getCurrentUser(c);

		// Get user's organizations (should have at least one auto-created)
		const organizations = await getUserOrganizations(user.id);

		if (organizations.length === 0) {
			return c.json({ error: "No organization found" }, 404);
		}

		// Get the first organization (auto-created one)
		const defaultOrgMembership = organizations[0];
		const defaultOrg = defaultOrgMembership.organization;

		// Generate name suggestions based on user info
		const suggestions = generateNameSuggestions({
			name: user.name || "User",
			email: user.email,
		});

		return c.json({
			organization: defaultOrg,
			suggestions: {
				names: suggestions,
				canInviteMembers: defaultOrg.type === "TEAM",
				isPersonalWorkspace: defaultOrg.type === "PERSONAL",
			},
		});
	} catch (error) {
		console.error("Get onboarding organization error:", error);
		return c.json({ error: "Failed to fetch organization data" }, 500);
	}
});

// Customize the user's default organization
app.put(
	"/organization/:organizationId",
	requireAuth,
	validateBody(customizeOrganizationSchema.parse),
	async (c) => {
		try {
			const user = getCurrentUser(c);
			const organizationId = c.req.param("organizationId");
			const data = c.get("validatedBody") as CustomizeOrganization;

			// Verify user owns this organization
			const userOrgs = await getUserOrganizations(user.id);
			const targetOrgMembership = userOrgs.find(
				(membership) => membership.organization.id === organizationId,
			);

			if (!targetOrgMembership) {
				return c.json(
					{ error: "Organization not found or access denied" },
					404,
				);
			}

			const targetOrg = targetOrgMembership.organization;

			// Generate unique slug if provided
			let finalSlug = targetOrg.slug; // Keep existing slug by default
			if (data.slug && data.slug !== targetOrg.slug) {
				finalSlug = await generateUniqueSlug(data.slug);
			}

			// Update organization
			const updatedOrg = await updateOrganization(organizationId, user.id, {
				name: data.name,
				description: data.description,
				slug: finalSlug,
			});

			return c.json({
				organization: updatedOrg,
				message: "Organization updated successfully",
			});
		} catch (error) {
			console.error("Customize organization error:", error);
			return c.json({ error: "Failed to update organization" }, 500);
		}
	},
);

// Check slug availability
app.get("/check-slug/:slug", requireAuth, async (c) => {
	try {
		const slug = c.req.param("slug");

		// Validate slug format
		const slugRegex = /^[a-z0-9-]+$/;
		if (!slugRegex.test(slug)) {
			return c.json({
				available: false,
				error: "Slug can only contain lowercase letters, numbers, and hyphens",
			});
		}

		// Check if slug is unique
		const uniqueSlug = await generateUniqueSlug(slug);
		const isAvailable = uniqueSlug === slug;

		return c.json({
			available: isAvailable,
			suggestion: isAvailable ? null : uniqueSlug,
		});
	} catch (error) {
		console.error("Check slug availability error:", error);
		return c.json({ error: "Failed to check slug availability" }, 500);
	}
});

// Get name suggestions based on current user
app.get("/name-suggestions", requireAuth, async (c) => {
	try {
		const user = getCurrentUser(c);

		const suggestions = generateNameSuggestions({
			name: user.name || "User",
			email: user.email,
		});

		return c.json({ suggestions });
	} catch (error) {
		console.error("Get name suggestions error:", error);
		return c.json({ error: "Failed to generate suggestions" }, 500);
	}
});

// Mark onboarding as completed
app.post("/complete", requireAuth, async (c) => {
	try {
		const user = getCurrentUser(c);

		// Log the completion event
		console.log(`Onboarding completed for user: ${user.id} (${user.email})`);

		// You could add a user flag or additional tracking here
		return c.json({
			message: "Onboarding completed successfully",
			redirectTo: "/dashboard",
		});
	} catch (error) {
		console.error("Complete onboarding error:", error);
		return c.json({ error: "Failed to complete onboarding" }, 500);
	}
});

export default app;
