{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "bun run --hot src/index.ts", "start": "bun run dist/index.js", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:start": "docker compose up -d", "db:watch": "docker compose up", "db:stop": "docker compose stop", "db:down": "docker compose down"}, "dependencies": {"@better-auth/expo": "^1.3.7", "@prisma/client": "^6.13.0", "@saas-template/schemas": "workspace:*", "@tanstack/zod-form-adapter": "^0.42.1", "better-auth": "^1.3.7", "dotenv": "^17.2.1", "hono": "^4.8.2", "zod": "^4.0.2"}, "devDependencies": {"tsdown": "^0.14.1", "typescript": "^5.8.2", "@types/bun": "^1.2.6", "prisma": "^6.13.0"}}