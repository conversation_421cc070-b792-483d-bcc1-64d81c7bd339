import type { ReactNode } from "react";
import { useCurrentUserRole, useHasPermission } from "@/hooks/use-session";

interface RBACGuardProps {
	children: ReactNode;
	organizationId: string;
	permission?: string;
	authRole?: "ADMIN" | "COLLABORATOR" | "USER";
	fallback?: ReactNode;
	requireAll?: boolean; // If true, user must have ALL specified permissions/roles
}

export function RBACGuard({
	children,
	organizationId,
	permission,
	authRole,
	fallback = null,
	requireAll = false,
}: RBACGuardProps) {
	const userRole = useCurrentUserRole(organizationId);
	const hasPermission = useHasPermission(organizationId, permission || "");

	// Check if user has required role
	const hasRole = authRole ? checkRole(userRole, authRole) : true;

	// Determine access based on requireAll flag
	const hasAccess = requireAll
		? hasRole && (permission ? hasPermission : true)
		: hasRole || (permission ? hasPermission : false);

	if (!hasAccess) {
		return <>{fallback}</>;
	}

	return <>{children}</>;
}

// Helper function to check role hierarchy
function checkRole(
	userRole: "ADMIN" | "COLLABORATOR" | "USER" | null,
	requiredRole: "ADMIN" | "COLLABORATOR" | "USER",
): boolean {
	if (!userRole) return false;

	const roleHierarchy = {
		ADMIN: 3,
		COLLABORATOR: 2,
		USER: 1,
	};

	return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
}

// Convenience components for common use cases
export function AdminOnly({
	children,
	organizationId,
	fallback,
}: {
	children: ReactNode;
	organizationId: string;
	fallback?: ReactNode;
}) {
	return (
		<RBACGuard
			organizationId={organizationId}
			authRole="ADMIN"
			fallback={fallback}
		>
			{children}
		</RBACGuard>
	);
}

export function CollaboratorOrAbove({
	children,
	organizationId,
	fallback,
}: {
	children: ReactNode;
	organizationId: string;
	fallback?: ReactNode;
}) {
	return (
		<RBACGuard
			organizationId={organizationId}
			authRole="COLLABORATOR"
			fallback={fallback}
		>
			{children}
		</RBACGuard>
	);
}

export function CanInviteUsers({
	children,
	organizationId,
	fallback,
}: {
	children: ReactNode;
	organizationId: string;
	fallback?: ReactNode;
}) {
	return (
		<RBACGuard
			organizationId={organizationId}
			permission="invitation:create"
			fallback={fallback}
		>
			{children}
		</RBACGuard>
	);
}

export function CanManageMembers({
	children,
	organizationId,
	fallback,
}: {
	children: ReactNode;
	organizationId: string;
	fallback?: ReactNode;
}) {
	return (
		<RBACGuard
			organizationId={organizationId}
			permission="organization:manage_members"
			fallback={fallback}
		>
			{children}
		</RBACGuard>
	);
}
