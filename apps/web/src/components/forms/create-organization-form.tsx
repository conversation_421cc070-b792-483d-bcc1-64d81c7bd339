import {
	type CreateOrganization,
	createOrganizationSchema,
} from "@saas-template/schemas";
import { useForm } from "@tanstack/react-form";

import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useCreateOrganization } from "@/hooks/use-organizations";

interface CreateOrganizationFormProps {
	onSuccess?: (organization: {
		id: string;
		name: string;
		slug: string;
		description?: string;
	}) => void;
	onCancel?: () => void;
}

export function CreateOrganizationForm({
	onSuccess,
	onCancel,
}: CreateOrganizationFormProps) {
	const createOrganization = useCreateOrganization();

	const form = useForm({
		defaultValues: {
			name: "",
			slug: "",
			description: "",
		} as CreateOrganization,
		onSubmit: async ({ value }) => {
			try {
				const result = await createOrganization.mutateAsync(value);
				toast.success("Organization created successfully");
				onSuccess?.(result.organization);
			} catch (error) {
				toast.error(
					error instanceof Error
						? error.message
						: "Failed to create organization",
				);
			}
		},

		validators: {
			onSubmit: createOrganizationSchema,
		},
	});

	// Auto-generate slug from name
	const handleNameChange = (name: string) => {
		const slug = name
			.toLowerCase()
			.replace(/[^a-z0-9\s-]/g, "")
			.replace(/\s+/g, "-")
			.replace(/-+/g, "-")
			.trim();

		form.setFieldValue("slug", slug);
	};

	return (
		<div className="space-y-6">
			<div>
				<h2 className="font-semibold text-lg">Create New Organization</h2>
				<p className="text-muted-foreground text-sm">
					Set up a new organization to collaborate with your team.
				</p>
			</div>

			<form
				onSubmit={(e) => {
					e.preventDefault();
					e.stopPropagation();
					form.handleSubmit();
				}}
				className="space-y-4"
			>
				<form.Field name="name">
					{(field) => (
						<div className="space-y-2">
							<Label htmlFor={field.name}>Organization Name</Label>
							<Input
								id={field.name}
								name={field.name}
								value={field.state.value}
								onBlur={field.handleBlur}
								onChange={(e) => {
									field.handleChange(e.target.value);
									handleNameChange(e.target.value);
								}}
								placeholder="Enter organization name"
							/>
							{field.state.meta.errors && (
								<p className="text-destructive text-sm">
									{String(field.state.meta.errors[0])}
								</p>
							)}
						</div>
					)}
				</form.Field>

				<form.Field name="slug">
					{(field) => (
						<div className="space-y-2">
							<Label htmlFor={field.name}>Organization Slug</Label>
							<Input
								id={field.name}
								name={field.name}
								value={field.state.value}
								onBlur={field.handleBlur}
								onChange={(e) => field.handleChange(e.target.value)}
								placeholder="organization-slug"
							/>
							<p className="text-muted-foreground text-xs">
								Used in URLs. Only lowercase letters, numbers, and hyphens
								allowed.
							</p>
							{field.state.meta.errors && (
								<p className="text-destructive text-sm">
									{String(field.state.meta.errors[0])}
								</p>
							)}
						</div>
					)}
				</form.Field>

				<form.Field name="description">
					{(field) => (
						<div className="space-y-2">
							<Label htmlFor={field.name}>Description (Optional)</Label>
							<Textarea
								id={field.name}
								name={field.name}
								value={field.state.value}
								onBlur={field.handleBlur}
								onChange={(e) => field.handleChange(e.target.value)}
								placeholder="Describe your organization..."
								rows={3}
							/>
							{field.state.meta.errors && (
								<p className="text-destructive text-sm">
									{String(field.state.meta.errors[0])}
								</p>
							)}
						</div>
					)}
				</form.Field>

				<div className="flex justify-end space-x-2">
					{onCancel && (
						<Button type="button" variant="outline" onClick={onCancel}>
							Cancel
						</Button>
					)}
					<Button type="submit" disabled={createOrganization.isPending}>
						{createOrganization.isPending
							? "Creating..."
							: "Create Organization"}
					</Button>
				</div>
			</form>
		</div>
	);
}
