import { type SignUp, signUpSchema } from "@saas-template/schemas";
import { useForm } from "@tanstack/react-form";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { authClient } from "@/lib/auth-client";

interface SignUpFormProps {
	onSwitchToSignIn?: () => void;
}

export function SignUpFormShared({ onSwitchToSignIn }: SignUpFormProps) {
	const navigate = useNavigate({ from: "/" });

	const form = useForm({
		defaultValues: {
			name: "",
			email: "",
			password: "",
		} as SignUp,
		onSubmit: async ({ value }) => {
			try {
				// Validate the data before submitting
				const validatedData = signUpSchema.parse(value);
				await authClient.signUp.email(validatedData, {
					onSuccess: () => {
						navigate({ to: "/dashboard" });
						toast.success("Account created successfully");
					},
					onError: (error) => {
						toast.error(error.error.message || error.error.statusText);
					},
				});
			} catch (error) {
				toast.error(
					error instanceof Error ? error.message : "Validation failed",
				);
			}
		},
	});

	return (
		<div className="space-y-6">
			<div className="space-y-2 text-center">
				<h1 className="font-bold text-3xl">Create Account</h1>
				<p className="text-muted-foreground">
					Enter your information to create your account
				</p>
			</div>

			<form
				onSubmit={(e) => {
					e.preventDefault();
					e.stopPropagation();
					form.handleSubmit();
				}}
				className="space-y-4"
			>
				<form.Field name="name">
					{(field) => (
						<div className="space-y-2">
							<Label htmlFor={field.name}>Full Name</Label>
							<Input
								id={field.name}
								name={field.name}
								value={field.state.value}
								onBlur={field.handleBlur}
								onChange={(e) => field.handleChange(e.target.value)}
								placeholder="Enter your full name"
							/>
							{field.state.meta.errors && (
								<p className="text-destructive text-sm">
									{String(field.state.meta.errors[0])}
								</p>
							)}
						</div>
					)}
				</form.Field>

				<form.Field name="email">
					{(field) => (
						<div className="space-y-2">
							<Label htmlFor={field.name}>Email</Label>
							<Input
								id={field.name}
								name={field.name}
								type="email"
								value={field.state.value}
								onBlur={field.handleBlur}
								onChange={(e) => field.handleChange(e.target.value)}
								placeholder="Enter your email"
							/>
							{field.state.meta.errors && (
								<p className="text-destructive text-sm">
									{String(field.state.meta.errors[0])}
								</p>
							)}
						</div>
					)}
				</form.Field>

				<form.Field name="password">
					{(field) => (
						<div className="space-y-2">
							<Label htmlFor={field.name}>Password</Label>
							<Input
								id={field.name}
								name={field.name}
								type="password"
								value={field.state.value}
								onBlur={field.handleBlur}
								onChange={(e) => field.handleChange(e.target.value)}
								placeholder="Create a password"
							/>
							{field.state.meta.errors && (
								<p className="text-destructive text-sm">
									{String(field.state.meta.errors[0])}
								</p>
							)}
						</div>
					)}
				</form.Field>

				<Button type="submit" className="w-full">
					Create Account
				</Button>
			</form>

			{onSwitchToSignIn && (
				<div className="text-center">
					<p className="text-muted-foreground text-sm">
						Already have an account?{" "}
						<button
							type="button"
							onClick={onSwitchToSignIn}
							className="font-medium text-primary underline underline-offset-4 hover:no-underline"
						>
							Sign in
						</button>
					</p>
				</div>
			)}
		</div>
	);
}
