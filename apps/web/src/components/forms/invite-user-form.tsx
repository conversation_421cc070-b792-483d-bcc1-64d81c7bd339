import {
	type CreateInvitation,
	createInvitationSchema,
	type Role,
} from "@saas-template/schemas";
import { useForm } from "@tanstack/react-form";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useCreateInvitation } from "@/hooks/use-invitations";
import { useHasPermission } from "@/hooks/use-session";

interface InviteUserFormProps {
	organizationId: string;
	onSuccess?: (invitation: {
		id: string;
		email: string;
		role: Role;
		status: string;
	}) => void;
	onCancel?: () => void;
}

export function InviteUserForm({
	organizationId,
	onSuccess,
	onCancel,
}: InviteUserFormProps) {
	const createInvitation = useCreateInvitation();
	const canInviteAnyRole = useHasPermission(
		organizationId,
		"user:invite_any_role",
	);

	const form = useForm({
		defaultValues: {
			email: "",
			role: "USER" as Role,
			organizationId,
		} as CreateInvitation,
		onSubmit: async ({ value }) => {
			try {
				// Validate the data before submitting
				const validatedData = createInvitationSchema.parse(value);
				const result = await createInvitation.mutateAsync(validatedData);
				toast.success("Invitation sent successfully");
				onSuccess?.(result.invitation);
			} catch (error) {
				toast.error(
					error instanceof Error ? error.message : "Failed to send invitation",
				);
			}
		},
	});

	return (
		<div className="space-y-6">
			<div>
				<h2 className="font-semibold text-lg">Invite User</h2>
				<p className="text-muted-foreground text-sm">
					Send an invitation to join this organization.
				</p>
			</div>

			<form
				onSubmit={(e) => {
					e.preventDefault();
					e.stopPropagation();
					form.handleSubmit();
				}}
				className="space-y-4"
			>
				<form.Field name="email">
					{(field) => (
						<div className="space-y-2">
							<Label htmlFor={field.name}>Email Address</Label>
							<Input
								id={field.name}
								name={field.name}
								type="email"
								value={field.state.value}
								onBlur={field.handleBlur}
								onChange={(e) => field.handleChange(e.target.value)}
								placeholder="<EMAIL>"
							/>
							{field.state.meta.errors && (
								<p className="text-destructive text-sm">
									{String(field.state.meta.errors[0])}
								</p>
							)}
						</div>
					)}
				</form.Field>

				<form.Field name="role">
					{(field) => (
						<div className="space-y-2">
							<Label htmlFor={field.name}>Role</Label>
							<Select
								value={field.state.value}
								onValueChange={(value) => field.handleChange(value as Role)}
							>
								<SelectTrigger>
									<SelectValue placeholder="Select a role" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="USER">User</SelectItem>
									{canInviteAnyRole && (
										<>
											<SelectItem value="COLLABORATOR">Collaborator</SelectItem>
											<SelectItem value="ADMIN">Admin</SelectItem>
										</>
									)}
								</SelectContent>
							</Select>
							<div className="text-muted-foreground text-xs">
								<div className="space-y-1">
									<p>
										<strong>User:</strong> Basic access to view resources
									</p>
									{canInviteAnyRole && (
										<>
											<p>
												<strong>Collaborator:</strong> Can edit resources and
												invite users
											</p>
											<p>
												<strong>Admin:</strong> Full access to manage
												organization
											</p>
										</>
									)}
								</div>
							</div>
							{field.state.meta.errors && (
								<p className="text-destructive text-sm">
									{String(field.state.meta.errors[0])}
								</p>
							)}
						</div>
					)}
				</form.Field>

				<div className="flex justify-end space-x-2">
					{onCancel && (
						<Button type="button" variant="outline" onClick={onCancel}>
							Cancel
						</Button>
					)}
					<Button type="submit" disabled={createInvitation.isPending}>
						{createInvitation.isPending ? "Sending..." : "Send Invitation"}
					</Button>
				</div>
			</form>
		</div>
	);
}
