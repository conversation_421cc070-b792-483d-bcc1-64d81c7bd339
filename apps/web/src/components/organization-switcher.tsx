import { Check, ChevronsUpDown, Plus } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@/components/ui/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { useExtendedSession, useSwitchOrganization } from "@/hooks/use-session";

export function OrganizationSwitcher() {
	const [open, setOpen] = useState(false);
	const { data: session, isLoading } = useExtendedSession();
	const switchOrganization = useSwitchOrganization();

	if (isLoading || !session) {
		return (
			<Button variant="outline" className="w-[200px] justify-between">
				Loading...
			</Button>
		);
	}

	const currentOrg = session.session.currentOrganization;
	const organizations = session.session.organizations;

	const handleSelect = async (organizationId: string) => {
		if (organizationId === currentOrg?.id) {
			setOpen(false);
			return;
		}

		try {
			await switchOrganization.mutateAsync(organizationId);
			toast.success("Organization switched successfully");
			setOpen(false);
		} catch (error) {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to switch organization",
			);
		}
	};

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					aria-expanded={open}
					className="w-[200px] justify-between"
				>
					<div className="flex items-center space-x-2">
						<div className="flex h-6 w-6 items-center justify-center rounded bg-primary font-medium text-primary-foreground text-xs">
							{currentOrg?.name.charAt(0).toUpperCase() || "?"}
						</div>
						<span className="truncate">
							{currentOrg?.name || "Select organization"}
						</span>
					</div>
					<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-[200px] p-0">
				<Command>
					<CommandInput placeholder="Search organizations..." />
					<CommandList>
						<CommandEmpty>No organizations found.</CommandEmpty>
						<CommandGroup heading="Organizations">
							{organizations.map((org) => (
								<CommandItem
									key={org.id}
									value={org.id}
									onSelect={() => handleSelect(org.id)}
								>
									<div className="flex flex-1 items-center space-x-2">
										<div className="flex h-6 w-6 items-center justify-center rounded bg-primary font-medium text-primary-foreground text-xs">
											{org.name.charAt(0).toUpperCase()}
										</div>
										<div className="flex-1">
											<div className="font-medium">{org.name}</div>
											<div className="text-muted-foreground text-xs capitalize">
												{org.role.toLowerCase()}
											</div>
										</div>
									</div>
									<Check
										className={`ml-2 h-4 w-4 ${
											currentOrg?.id === org.id ? "opacity-100" : "opacity-0"
										}`}
									/>
								</CommandItem>
							))}
						</CommandGroup>
						<CommandSeparator />
						<CommandGroup>
							<CommandItem
								onSelect={() => {
									setOpen(false);
									// Navigate to create organization page
									window.location.href = "/organizations/new";
								}}
							>
								<Plus className="mr-2 h-4 w-4" />
								Create Organization
							</CommandItem>
						</CommandGroup>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
