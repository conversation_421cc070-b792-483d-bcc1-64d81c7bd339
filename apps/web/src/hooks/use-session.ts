import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { authClient } from "@/lib/auth-client";

const API_BASE = import.meta.env.VITE_SERVER_URL;

// Types
export interface SessionOrganization {
	id: string;
	name: string;
	slug: string;
	role: "ADMIN" | "COLLABORATOR" | "USER";
	joinedAt: string;
}

export interface CurrentOrganization {
	id: string;
	name: string;
	slug: string;
}

export interface ExtendedSession {
	user: {
		id: string;
		name: string;
		email: string;
		image?: string;
	};
	session: {
		organizations: SessionOrganization[];
		currentOrganization: CurrentOrganization | null;
	};
}

// API functions
async function fetchWithAuth(url: string, options: RequestInit = {}) {
	const session = await authClient.getSession();
	if (!session) {
		throw new Error("Not authenticated");
	}

	const response = await fetch(`${API_BASE}${url}`, {
		...options,
		headers: {
			...options.headers,
			"Content-Type": "application/json",
			Cookie: document.cookie,
		},
		credentials: "include",
	});

	if (!response.ok) {
		const error = await response
			.json()
			.catch(() => ({ error: "Request failed" }));
		throw new Error(error.error || "Request failed");
	}

	return response.json();
}

// Hooks
export function useExtendedSession() {
	return useQuery({
		queryKey: ["session"],
		queryFn: () => fetchWithAuth("/api/session"),
		select: (data) => data as ExtendedSession,
	});
}

export function useSwitchOrganization() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (organizationId: string) =>
			fetchWithAuth("/api/session/switch-organization", {
				method: "POST",
				body: JSON.stringify({ organizationId }),
			}),
		onSuccess: () => {
			// Invalidate session and organization-related queries
			queryClient.invalidateQueries({ queryKey: ["session"] });
			queryClient.invalidateQueries({ queryKey: ["organizations"] });
		},
	});
}

// Helper hooks for role-based access
export function useCurrentUserRole(organizationId?: string) {
	const { data: session } = useExtendedSession();

	if (!session || !organizationId) return null;

	const org = session.session.organizations.find(
		(o) => o.id === organizationId,
	);
	return org?.role || null;
}

export function useHasPermission(organizationId: string, permission: string) {
	const role = useCurrentUserRole(organizationId);

	if (!role) return false;

	// Define permissions based on role (matching server-side logic)
	const permissions = {
		ADMIN: [
			"organization:create",
			"organization:read",
			"organization:update",
			"organization:delete",
			"organization:manage_members",
			"invitation:create",
			"invitation:read",
			"invitation:revoke",
			"user:invite_any_role",
			"user:manage_roles",
		],
		COLLABORATOR: [
			"organization:read",
			"organization:update",
			"invitation:create",
			"invitation:read",
			"user:invite_user_role",
		],
		USER: ["organization:read"],
	};

	return permissions[role]?.includes(permission) || false;
}

export function useIsAdmin(organizationId?: string) {
	const role = useCurrentUserRole(organizationId);
	return role === "ADMIN";
}

export function useIsCollaborator(organizationId?: string) {
	const role = useCurrentUserRole(organizationId);
	return role === "COLLABORATOR" || role === "ADMIN";
}

export function useCanInviteUsers(organizationId?: string) {
	return useHasPermission(organizationId || "", "invitation:create");
}

export function useCanManageMembers(organizationId?: string) {
	return useHasPermission(organizationId || "", "organization:manage_members");
}
