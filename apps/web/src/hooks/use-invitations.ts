import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { authClient } from "@/lib/auth-client";

const API_BASE = import.meta.env.VITE_SERVER_URL;

// Types
export interface Invitation {
	id: string;
	email: string;
	role: "ADMIN" | "COLLABORATOR" | "USER";
	status: "PENDING" | "ACCEPTED" | "EXPIRED" | "REVOKED";
	token: string;
	expiresAt: string;
	createdAt: string;
	acceptedAt?: string;
	organization: {
		id: string;
		name: string;
		slug: string;
		description?: string;
	};
	invitedBy: {
		id: string;
		name: string;
		email: string;
	};
}

export interface CreateInvitationData {
	email: string;
	role: "ADMIN" | "COLLABORATOR" | "USER";
	organizationId: string;
}

export interface InvitationFilters {
	status?: "PENDING" | "ACCEPTED" | "EXPIRED" | "REVOKED";
	email?: string;
	role?: "ADMIN" | "COLLABORATOR" | "USER";
}

// API functions
async function fetchWithAuth(url: string, options: RequestInit = {}) {
	const session = await authClient.getSession();
	if (!session) {
		throw new Error("Not authenticated");
	}

	const response = await fetch(`${API_BASE}${url}`, {
		...options,
		headers: {
			...options.headers,
			"Content-Type": "application/json",
			Cookie: document.cookie,
		},
		credentials: "include",
	});

	if (!response.ok) {
		const error = await response
			.json()
			.catch(() => ({ error: "Request failed" }));
		throw new Error(error.error || "Request failed");
	}

	return response.json();
}

// Public API function (no auth required)
async function fetchPublic(url: string, options: RequestInit = {}) {
	const response = await fetch(`${API_BASE}${url}`, {
		...options,
		headers: {
			...options.headers,
			"Content-Type": "application/json",
		},
	});

	if (!response.ok) {
		const error = await response
			.json()
			.catch(() => ({ error: "Request failed" }));
		throw new Error(error.error || "Request failed");
	}

	return response.json();
}

// Hooks
export function useCreateInvitation() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: CreateInvitationData) =>
			fetchWithAuth("/api/invitations", {
				method: "POST",
				body: JSON.stringify(data),
			}),
		onSuccess: (_, variables) => {
			queryClient.invalidateQueries({
				queryKey: ["invitations", variables.organizationId],
			});
		},
	});
}

export function useInvitationByToken(token: string) {
	return useQuery({
		queryKey: ["invitation", token],
		queryFn: () => fetchPublic(`/api/invitations/token/${token}`),
		select: (data) => data.invitation as Invitation,
		enabled: !!token,
		retry: false, // Don't retry on invalid tokens
	});
}

export function useAcceptInvitation() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (token: string) =>
			fetchWithAuth("/api/invitations/accept", {
				method: "POST",
				body: JSON.stringify({ token }),
			}),
		onSuccess: () => {
			// Invalidate organizations and session data
			queryClient.invalidateQueries({ queryKey: ["organizations"] });
			queryClient.invalidateQueries({ queryKey: ["session"] });
		},
	});
}

export function useOrganizationInvitations(
	organizationId: string,
	filters: InvitationFilters = {},
) {
	const queryParams = new URLSearchParams();

	if (filters.status) queryParams.append("status", filters.status);
	if (filters.email) queryParams.append("email", filters.email);
	if (filters.role) queryParams.append("role", filters.role);

	const queryString = queryParams.toString();
	const url = `/api/invitations/organization/${organizationId}${queryString ? `?${queryString}` : ""}`;

	return useQuery({
		queryKey: ["invitations", organizationId, filters],
		queryFn: () => fetchWithAuth(url),
		select: (data) => data.invitations as Invitation[],
		enabled: !!organizationId,
	});
}

export function useRevokeInvitation() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (invitationId: string) =>
			fetchWithAuth(`/api/invitations/${invitationId}`, {
				method: "DELETE",
			}),
		onSuccess: () => {
			// Invalidate all invitation queries
			queryClient.invalidateQueries({ queryKey: ["invitations"] });
		},
	});
}

export function useCleanupExpiredInvitations() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: () =>
			fetchWithAuth("/api/invitations/cleanup-expired", {
				method: "POST",
			}),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["invitations"] });
		},
	});
}
