import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { authClient } from "@/lib/auth-client";

const API_BASE = import.meta.env.VITE_SERVER_URL;

// Types
export interface Organization {
	id: string;
	name: string;
	slug: string;
	description?: string;
	role: "ADMIN" | "COLLABORATOR" | "USER";
	joinedAt: string;
	memberCount: number;
}

export interface OrganizationMember {
	id: string;
	role: "ADMIN" | "COLLABORATOR" | "USER";
	joinedAt: string;
	user: {
		id: string;
		name: string;
		email: string;
		image?: string;
	};
}

export interface CreateOrganizationData {
	name: string;
	slug: string;
	description?: string;
}

// API functions
async function fetchWithAuth(url: string, options: RequestInit = {}) {
	const session = await authClient.getSession();
	if (!session) {
		throw new Error("Not authenticated");
	}

	const response = await fetch(`${API_BASE}${url}`, {
		...options,
		headers: {
			...options.headers,
			"Content-Type": "application/json",
			// Add auth headers from session
			Cookie: document.cookie,
		},
		credentials: "include",
	});

	if (!response.ok) {
		const error = await response
			.json()
			.catch(() => ({ error: "Request failed" }));
		throw new Error(error.error || "Request failed");
	}

	return response.json();
}

// Hooks
export function useOrganizations() {
	return useQuery({
		queryKey: ["organizations"],
		queryFn: () => fetchWithAuth("/api/organizations"),
		select: (data) => data.organizations as Organization[],
	});
}

export function useOrganization(organizationId: string) {
	return useQuery({
		queryKey: ["organization", organizationId],
		queryFn: () => fetchWithAuth(`/api/organizations/${organizationId}`),
		select: (data) => data.organization,
		enabled: !!organizationId,
	});
}

export function useOrganizationMembers(organizationId: string) {
	return useQuery({
		queryKey: ["organization-members", organizationId],
		queryFn: () =>
			fetchWithAuth(`/api/organizations/${organizationId}/members`),
		select: (data) => data.members as OrganizationMember[],
		enabled: !!organizationId,
	});
}

export function useCreateOrganization() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: CreateOrganizationData) =>
			fetchWithAuth("/api/organizations", {
				method: "POST",
				body: JSON.stringify(data),
			}),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["organizations"] });
		},
	});
}

export function useUpdateOrganization(organizationId: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: Partial<CreateOrganizationData>) =>
			fetchWithAuth(`/api/organizations/${organizationId}`, {
				method: "PUT",
				body: JSON.stringify(data),
			}),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["organization", organizationId],
			});
			queryClient.invalidateQueries({ queryKey: ["organizations"] });
		},
	});
}

export function useDeleteOrganization() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (organizationId: string) =>
			fetchWithAuth(`/api/organizations/${organizationId}`, {
				method: "DELETE",
			}),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["organizations"] });
		},
	});
}

export function useAddMember(organizationId: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: {
			userId: string;
			role: "ADMIN" | "COLLABORATOR" | "USER";
		}) =>
			fetchWithAuth(`/api/organizations/${organizationId}/members`, {
				method: "POST",
				body: JSON.stringify(data),
			}),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["organization-members", organizationId],
			});
		},
	});
}

export function useUpdateMemberRole(organizationId: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			memberId,
			role,
		}: {
			memberId: string;
			role: "ADMIN" | "COLLABORATOR" | "USER";
		}) =>
			fetchWithAuth(
				`/api/organizations/${organizationId}/members/${memberId}`,
				{
					method: "PUT",
					body: JSON.stringify({ role }),
				},
			),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["organization-members", organizationId],
			});
		},
	});
}

export function useRemoveMember(organizationId: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (memberId: string) =>
			fetchWithAuth(
				`/api/organizations/${organizationId}/members/${memberId}`,
				{
					method: "DELETE",
				},
			),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["organization-members", organizationId],
			});
		},
	});
}
