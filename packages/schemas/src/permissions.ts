/**
 * Role-based access control (RBAC) permissions for the SaaS application
 * Shared between frontend and backend to ensure consistency
 */

// Permission definitions for each role
export const PERMISSIONS = {
	ADMIN: [
		"organization:create",
		"organization:read",
		"organization:update",
		"organization:delete",
		"organization:manage_members",
		"invitation:create",
		"invitation:read",
		"invitation:revoke",
		"user:invite_any_role",
		"user:manage_roles",
	],
	COLLABORATOR: [
		"organization:read",
		"organization:update",
		"invitation:create",
		"invitation:read",
		"user:invite_user_role", // Can only invite users with USER role
	],
	USER: ["organization:read"],
} as const;

// Type definitions for permissions
export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS][number];
export type Role = keyof typeof PERMISSIONS;

// Helper function to check if a role has a specific permission
export function hasPermission(role: Role, permission: Permission): boolean {
	return PERMISSIONS[role].includes(permission);
}

// Helper function to get all permissions for a role
export function getPermissionsForRole(role: Role): readonly Permission[] {
	return PERMISSIONS[role];
}

// Helper function to check if a role can perform an action on a resource
export function canPerformAction(
	role: Role,
	resource: string,
	action: string,
): boolean {
	const permission = `${resource}:${action}` as Permission;
	return hasPermission(role, permission);
}

// Helper function to get the highest role from a list of roles
export function getHighestRole(roles: Role[]): Role {
	const roleHierarchy: Record<Role, number> = {
		USER: 1,
		COLLABORATOR: 2,
		ADMIN: 3,
	};

	return roles.reduce((highest, current) => {
		return roleHierarchy[current] > roleHierarchy[highest] ? current : highest;
	}, "USER" as Role);
}

// Permission groups for easier management
export const PERMISSION_GROUPS = {
	ORGANIZATION_MANAGEMENT: [
		"organization:create",
		"organization:read",
		"organization:update",
		"organization:delete",
		"organization:manage_members",
	],
	INVITATION_MANAGEMENT: [
		"invitation:create",
		"invitation:read",
		"invitation:revoke",
	],
	USER_MANAGEMENT: [
		"user:invite_any_role",
		"user:invite_user_role",
		"user:manage_roles",
	],
} as const;

// Helper to check if a role has all permissions in a group
export function hasPermissionGroup(
	role: Role,
	group: keyof typeof PERMISSION_GROUPS,
): boolean {
	const groupPermissions = PERMISSION_GROUPS[group];
	return groupPermissions.every((permission) =>
		hasPermission(role, permission as Permission),
	);
}

// Export all permission strings as a flat array for validation
export const ALL_PERMISSIONS = Object.values(PERMISSIONS).flat() as Permission[];

// Validation helper to check if a permission string is valid
export function isValidPermission(permission: string): permission is Permission {
	return ALL_PERMISSIONS.includes(permission as Permission);
}
