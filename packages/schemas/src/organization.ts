import { z } from "zod";

// Role enum schema
export const roleSchema = z.enum(["ADMIN", "COLLABORATOR", "USER"]);

// Organization type enum schema
export const organizationTypeSchema = z.enum([
	"PERSONAL",
	"TEAM",
	"ENTERPRISE",
]);

// Base organization schema
export const organizationSchema = z.object({
	id: z.string().cuid(),
	name: z.string().min(1, "Name is required").max(100, "Name too long"),
	slug: z
		.string()
		.min(1, "Slug is required")
		.max(50, "Slug too long")
		.regex(
			/^[a-z0-9-]+$/,
			"Slug must contain only lowercase letters, numbers, and hyphens",
		),
	description: z.string().max(500, "Description too long").optional(),
	type: organizationTypeSchema.default("PERSONAL"),
	lastActivityAt: z.date().optional(),
	createdAt: z.date(),
	updatedAt: z.date(),
});

// Organization creation schema
export const createOrganizationSchema = z.object({
	name: z.string().min(1, "Name is required").max(100, "Name too long"),
	slug: z
		.string()
		.min(1, "Slug is required")
		.max(50, "Slug too long")
		.regex(
			/^[a-z0-9-]+$/,
			"Slug must contain only lowercase letters, numbers, and hyphens",
		),
	description: z.string().max(500, "Description too long").optional(),
	type: organizationTypeSchema.default("PERSONAL").optional(),
});

// Organization update schema
export const updateOrganizationSchema = z.object({
	name: z
		.string()
		.min(1, "Name is required")
		.max(100, "Name too long")
		.optional(),
	slug: z
		.string()
		.min(1, "Slug is required")
		.max(50, "Slug too long")
		.regex(
			/^[a-z0-9-]+$/,
			"Slug must contain only lowercase letters, numbers, and hyphens",
		)
		.optional(),
	description: z.string().max(500, "Description too long").optional(),
	type: organizationTypeSchema.optional(),
});

// Organization customization schema for onboarding
export const customizeOrganizationSchema = z.object({
	name: z
		.string()
		.min(1, "Organization name is required")
		.max(100, "Organization name must be less than 100 characters"),
	description: z
		.string()
		.max(500, "Description must be less than 500 characters")
		.optional(),
	slug: z
		.string()
		.min(1, "Organization slug is required")
		.max(50, "Organization slug must be less than 50 characters")
		.regex(
			/^[a-z0-9-]+$/,
			"Slug can only contain lowercase letters, numbers, and hyphens",
		)
		.optional(),
});

// Organization member schema
export const organizationMemberSchema = z.object({
	id: z.string().cuid(),
	userId: z.string().cuid(),
	organizationId: z.string().cuid(),
	role: roleSchema,
	joinedAt: z.date(),
	updatedAt: z.date(),
});

// Add member schema
export const addMemberSchema = z.object({
	userId: z.string().cuid("Invalid user ID"),
	role: roleSchema,
});

// Update member role schema
export const updateMemberRoleSchema = z.object({
	role: roleSchema,
});

// Organization with member count
export const organizationWithStatsSchema = organizationSchema.extend({
	memberCount: z.number().int().min(0),
	invitationCount: z.number().int().min(0).optional(),
});

// Organization member with user info
export const organizationMemberWithUserSchema = organizationMemberSchema.extend(
	{
		user: z.object({
			id: z.string().cuid(),
			name: z.string(),
			email: z.string().email(),
			image: z.string().url().optional(),
		}),
	},
);

// Type exports
export type Role = z.infer<typeof roleSchema>;
export type OrganizationType = z.infer<typeof organizationTypeSchema>;
export type Organization = z.infer<typeof organizationSchema>;
export type CreateOrganization = z.infer<typeof createOrganizationSchema>;
export type UpdateOrganization = z.infer<typeof updateOrganizationSchema>;
export type CustomizeOrganization = z.infer<typeof customizeOrganizationSchema>;
export type OrganizationMember = z.infer<typeof organizationMemberSchema>;
export type AddMember = z.infer<typeof addMemberSchema>;
export type UpdateMemberRole = z.infer<typeof updateMemberRoleSchema>;
export type OrganizationWithStats = z.infer<typeof organizationWithStatsSchema>;
export type OrganizationMemberWithUser = z.infer<
	typeof organizationMemberWithUserSchema
>;
