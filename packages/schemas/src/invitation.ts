import { z } from "zod";
import { roleSchema } from "./organization";

// Invitation status enum schema
export const invitationStatusSchema = z.enum([
	"PENDING",
	"ACCEPTED",
	"EXPIRED",
	"REVOKED",
]);

// Base invitation schema
export const invitationSchema = z.object({
	id: z.string().cuid(),
	email: z.string().email("Invalid email address"),
	organizationId: z.string().cuid(),
	invitedById: z.string().cuid(),
	role: roleSchema,
	token: z.string(),
	status: invitationStatusSchema,
	expiresAt: z.date(),
	createdAt: z.date(),
	updatedAt: z.date(),
	acceptedAt: z.date().optional(),
});

// Create invitation schema
export const createInvitationSchema = z.object({
	email: z.string().email("Invalid email address"),
	role: roleSchema,
	organizationId: z.string().cuid("Invalid organization ID"),
});

// Accept invitation schema
export const acceptInvitationSchema = z.object({
	token: z.string().min(1, "Token is required"),
});

// Invitation filters schema
export const invitationFiltersSchema = z.object({
	status: invitationStatusSchema.optional(),
	email: z.string().optional(),
	role: roleSchema.optional(),
});

// Invitation with organization info
export const invitationWithOrganizationSchema = invitationSchema.extend({
	organization: z.object({
		id: z.string().cuid(),
		name: z.string(),
		slug: z.string(),
		description: z.string().optional(),
	}),
	invitedBy: z.object({
		id: z.string().cuid(),
		name: z.string(),
		email: z.string().email(),
	}),
});

// Invitation response schema (for public invitation preview)
export const invitationPreviewSchema = z.object({
	id: z.string().cuid(),
	email: z.string().email(),
	role: roleSchema,
	status: invitationStatusSchema,
	expiresAt: z.date(),
	organization: z.object({
		id: z.string().cuid(),
		name: z.string(),
		slug: z.string(),
		description: z.string().optional(),
	}),
	invitedBy: z.object({
		id: z.string().cuid(),
		name: z.string(),
		email: z.string().email(),
	}),
});

// Type exports
export type InvitationStatus = z.infer<typeof invitationStatusSchema>;
export type Invitation = z.infer<typeof invitationSchema>;
export type CreateInvitation = z.infer<typeof createInvitationSchema>;
export type AcceptInvitation = z.infer<typeof acceptInvitationSchema>;
export type InvitationFilters = z.infer<typeof invitationFiltersSchema>;
export type InvitationWithOrganization = z.infer<
	typeof invitationWithOrganizationSchema
>;
export type InvitationPreview = z.infer<typeof invitationPreviewSchema>;
