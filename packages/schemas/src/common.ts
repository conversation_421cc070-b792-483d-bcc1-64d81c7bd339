import { z } from "zod";

// Common ID schemas
export const cuidSchema = z.string().cuid();
export const uuidSchema = z.string().uuid();

// Pagination schemas
export const paginationSchema = z.object({
	page: z.number().int().min(1).default(1),
	limit: z.number().int().min(1).max(100).default(10),
});

export const paginationResponseSchema = z.object({
	page: z.number().int().min(1),
	limit: z.number().int().min(1),
	total: z.number().int().min(0),
	totalPages: z.number().int().min(0),
});

// Sort schemas
export const sortOrderSchema = z.enum(["asc", "desc"]);

export const sortSchema = z.object({
	field: z.string(),
	order: sortOrderSchema.default("asc"),
});

// Search schema
export const searchSchema = z.object({
	query: z.string().min(1, "Search query is required"),
	fields: z.array(z.string()).optional(),
});

// Date range schema
export const dateRangeSchema = z
	.object({
		from: z.date(),
		to: z.date(),
	})
	.refine((data) => data.from <= data.to, {
		message: "From date must be before or equal to to date",
		path: ["from"],
	});

// API response schemas
export const successResponseSchema = z.object({
	success: z.literal(true),
	message: z.string().optional(),
});

export const errorResponseSchema = z.object({
	success: z.literal(false),
	error: z.string(),
	details: z.unknown().optional(),
});

export const apiResponseSchema = z.union([
	successResponseSchema,
	errorResponseSchema,
]);

// File upload schema
export const fileUploadSchema = z.object({
	name: z.string().min(1, "File name is required"),
	size: z.number().int().min(1, "File size must be greater than 0"),
	type: z.string().min(1, "File type is required"),
	url: z.string().url("Invalid file URL").optional(),
});

// Email schema
export const emailSchema = z.string().email("Invalid email address");

// URL schema
export const urlSchema = z.string().url("Invalid URL");

// Phone number schema (basic)
export const phoneSchema = z
	.string()
	.regex(/^\+?[1-9]\d{1,14}$/, "Invalid phone number format");

// Color schema (hex)
export const colorSchema = z
	.string()
	.regex(
		/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
		"Invalid color format (use hex format like #FF0000)",
	);

// Type exports
export type Pagination = z.infer<typeof paginationSchema>;
export type PaginationResponse = z.infer<typeof paginationResponseSchema>;
export type SortOrder = z.infer<typeof sortOrderSchema>;
export type Sort = z.infer<typeof sortSchema>;
export type Search = z.infer<typeof searchSchema>;
export type DateRange = z.infer<typeof dateRangeSchema>;
export type SuccessResponse = z.infer<typeof successResponseSchema>;
export type ErrorResponse = z.infer<typeof errorResponseSchema>;
export type ApiResponse = z.infer<typeof apiResponseSchema>;
export type FileUpload = z.infer<typeof fileUploadSchema>;
