# @saas-template/schemas

Shared Zod validation schemas for the SaaS template monorepo. This package provides consistent validation logic across all applications (server, web, native) to ensure data integrity and type safety.

## Features

- 🔒 **Type-safe validation** with Zod
- 🏢 **Organization & RBAC schemas** for multi-tenant applications
- 📧 **Invitation system schemas** with role-based permissions
- 👤 **Authentication schemas** for user management
- 🔄 **Shared types** with automatic TypeScript inference
- 📦 **Zero build step** - works directly with TypeScript

## Installation

This package is automatically available in all workspace applications:

```json
{
  "dependencies": {
    "@saas-template/schemas": "workspace:*"
  }
}
```

## Usage

### Basic Import

```typescript
import { 
  createOrganizationSchema, 
  signUpSchema, 
  type CreateOrganization,
  type SignUp 
} from "@saas-template/schemas";
```

### Server-Side Validation

```typescript
// In API routes
import { createOrganizationSchema } from "@saas-template/schemas";

app.post("/organizations", async (c) => {
  const body = await c.req.json();
  
  // Validate request body
  const validData = createOrganizationSchema.parse(body);
  
  // Use validated data
  const organization = await createOrganization(validData);
  return c.json({ organization });
});
```

### Client-Side Forms

```typescript
// With TanStack Form
import { useForm } from "@tanstack/react-form";
import { signUpSchema, type SignUp } from "@saas-template/schemas";

const form = useForm({
  defaultValues: {
    name: "",
    email: "",
    password: "",
  } as SignUp,
  onSubmit: async ({ value }) => {
    // Validate before submitting
    const validatedData = signUpSchema.parse(value);
    // Submit validated data
  },
});
```

## Available Schemas

### Authentication (`auth.ts`)
- `userSchema`, `signUpSchema`, `signInSchema`
- `createUserSchema`, `updateUserSchema`
- `changePasswordSchema`, `resetPasswordSchema`

### Organization Management (`organization.ts`)
- `organizationSchema`, `createOrganizationSchema`
- `roleSchema` (ADMIN, COLLABORATOR, USER)
- `addMemberSchema`, `updateMemberRoleSchema`

### Invitation System (`invitation.ts`)
- `invitationSchema`, `createInvitationSchema`
- `acceptInvitationSchema`, `invitationStatusSchema`

### Common Utilities (`common.ts`)
- `paginationSchema`, `sortSchema`, `searchSchema`
- `emailSchema`, `urlSchema`, `fileUploadSchema`

## Type Exports

All schemas automatically export corresponding TypeScript types:

```typescript
import type { 
  User,
  CreateOrganization,
  Invitation,
  Role,
  InvitationStatus 
} from "@saas-template/schemas";
```

## Examples

### Creating a Form Component

```typescript
import { useForm } from "@tanstack/react-form";
import { createOrganizationSchema, type CreateOrganization } from "@saas-template/schemas";

export function CreateOrganizationForm() {
  const form = useForm({
    defaultValues: {
      name: "",
      slug: "",
      description: "",
    } as CreateOrganization,
    onSubmit: async ({ value }) => {
      const validatedData = createOrganizationSchema.parse(value);
      // Submit validated data
    },
  });

  return (
    <form onSubmit={form.handleSubmit}>
      <form.Field name="name">
        {(field) => (
          <input
            value={field.state.value}
            onChange={(e) => field.handleChange(e.target.value)}
          />
        )}
      </form.Field>
    </form>
  );
}
```

### API Route with Validation

```typescript
import { createInvitationSchema } from "@saas-template/schemas";

app.post("/invitations", async (c) => {
  try {
    const body = await c.req.json();
    const validData = createInvitationSchema.parse(body);
    
    const invitation = await InvitationService.create(userId, validData);
    return c.json({ invitation });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return c.json({ error: "Validation failed", details: error.errors }, 400);
    }
    throw error;
  }
});
```

This shared package ensures that validation logic stays consistent across your entire application stack while providing excellent TypeScript support and developer experience.
